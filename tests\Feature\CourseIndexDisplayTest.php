<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseIndexDisplayTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $categories;
    protected $courses;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Create categories with colors and icons
        $this->categories = collect([
            Category::factory()->create([
                'name' => 'Web Development',
                'color' => '#007bff',
                'icon' => 'uil-laptop',
                'is_active' => true
            ]),
            Category::factory()->create([
                'name' => 'Data Science',
                'color' => '#28a745',
                'icon' => 'uil-chart-line',
                'is_active' => true
            ]),
            Category::factory()->create([
                'name' => 'Design',
                'color' => '#dc3545',
                'icon' => 'uil-palette',
                'is_active' => true
            ])
        ]);

        // Create instructors
        $instructors = User::factory()->count(2)->create([
            'role' => 'admin'
        ]);

        // Create courses for each category
        $this->courses = collect();
        $this->categories->each(function ($category) use ($instructors) {
            $coursesForCategory = Course::factory()->count(2)->create([
                'category_id' => $category->id,
                'instructor_id' => $instructors->random()->id,
                'status' => $this->faker->randomElement(['draft', 'published', 'archived']),
                'is_featured' => $this->faker->boolean(30),
                'is_free' => $this->faker->boolean(40),
                'duration' => $this->faker->randomFloat(1, 1, 50),
                'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced'])
            ]);
            $this->courses = $this->courses->merge($coursesForCategory);
        });
    }

    /** @test */
    public function course_index_displays_categories_with_colors_and_icons()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Check that categories are displayed in the filter dropdown
        $this->categories->each(function ($category) use ($response) {
            $response->assertSee($category->name);
            $response->assertSee('data-color="' . $category->color . '"', false);
            $response->assertSee('data-icon="' . $category->icon . '"', false);
        });
    }

    /** @test */
    public function course_index_displays_category_overview_section()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);
        $response->assertSee('Courses by Category');

        // Check that category cards are displayed
        $this->categories->each(function ($category) use ($response) {
            $response->assertSee($category->name);
            $response->assertSee($category->icon, false);
            $response->assertSee('background-color: ' . $category->color, false);
        });
    }

    /** @test */
    public function course_index_displays_courses_with_enhanced_category_badges()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Check that courses display category information with colors and icons
        $this->courses->each(function ($course) use ($response) {
            $response->assertSee($course->title);
            $response->assertSee($course->category->name);
            $response->assertSee($course->category->icon, false);
            $response->assertSee('background-color: ' . $course->category->color, false);
        });
    }

    /** @test */
    public function course_index_displays_course_metadata()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Check that courses display duration and difficulty level
        $this->courses->each(function ($course) use ($response) {
            if ($course->duration) {
                $response->assertSee($course->duration . 'h');
            }
            if ($course->difficulty_level) {
                $response->assertSee(ucfirst($course->difficulty_level));
            }
        });
    }

    /** @test */
    public function course_index_displays_statistics()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Check statistics cards
        $response->assertSee('Total Courses');
        $response->assertSee('Published');
        $response->assertSee('Drafts');
        $response->assertSee('Featured');

        // Check that actual counts are displayed
        $totalCourses = Course::count();
        $publishedCourses = Course::where('status', 'published')->count();
        $draftCourses = Course::where('status', 'draft')->count();
        $featuredCourses = Course::where('is_featured', true)->count();

        $response->assertSee((string) $totalCourses);
        $response->assertSee((string) $publishedCourses);
        $response->assertSee((string) $draftCourses);
        $response->assertSee((string) $featuredCourses);
    }

    /** @test */
    public function course_index_includes_category_filter_functionality()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Check that filter JavaScript functions are included
        $response->assertSee('filterByCategory', false);
        $response->assertSee('categoryFilter', false);
        $response->assertSee('filterTable', false);
    }

    /** @test */
    public function course_index_displays_empty_state_when_no_courses()
    {
        // Delete all courses
        Course::query()->delete();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);
        $response->assertSee('No courses found');
        $response->assertSee('Start by creating your first course');
    }

    /** @test */
    public function course_index_loads_categories_with_course_counts()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.index'));

        $response->assertStatus(200);

        // Verify that categories show course counts
        $this->categories->each(function ($category) use ($response) {
            $courseCount = $category->courses()->count();
            $response->assertSee($courseCount . ' courses');
        });
    }
}
