<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class ZoomMeeting extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'zoom_meeting_id',
        'topic',
        'description',
        'start_time',
        'duration',
        'timezone',
        'join_url',
        'start_url',
        'password',
        'status',
        'recording_url',
        'recording_available',
        'minimum_attendance_minutes',
        'attendance_required_for_completion',
        'zoom_response',
        'created_by',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'recording_available' => 'boolean',
        'attendance_required_for_completion' => 'boolean',
        'zoom_response' => 'array',
        'duration' => 'integer',
        'minimum_attendance_minutes' => 'integer',
    ];

    // Relationships
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(MeetingAttendance::class);
    }

    // Scopes
    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now())
                    ->where('status', 'scheduled');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'started');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'ended');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('start_time', today());
    }

    // Accessors & Mutators
    public function getIsUpcomingAttribute(): bool
    {
        return $this->start_time > now() && $this->status === 'scheduled';
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'started';
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'ended';
    }

    public function getCanJoinAttribute(): bool
    {
        $now = now();
        $meetingStart = $this->start_time;
        $meetingEnd = $this->start_time->addMinutes($this->duration);
        
        // Allow joining 15 minutes before and during the meeting
        return $now >= $meetingStart->subMinutes(15) && 
               $now <= $meetingEnd && 
               in_array($this->status, ['scheduled', 'started']);
    }

    public function getFormattedStartTimeAttribute(): string
    {
        return $this->start_time->format('M d, Y \a\t g:i A');
    }

    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;
        
        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }
        
        return $minutes . ' minutes';
    }

    // Methods
    public function getUserAttendance(User $user): ?MeetingAttendance
    {
        return $this->attendances()->where('user_id', $user->id)->first();
    }

    public function hasUserAttended(User $user): bool
    {
        $attendance = $this->getUserAttendance($user);
        return $attendance && $attendance->attended_full_session;
    }

    public function getTotalAttendees(): int
    {
        return $this->attendances()->distinct('user_id')->count();
    }

    public function getQualifiedAttendees(): int
    {
        return $this->attendances()->where('attended_full_session', true)->count();
    }

    public function updateStatus(): void
    {
        $now = now();
        $meetingStart = $this->start_time;
        $meetingEnd = $this->start_time->copy()->addMinutes($this->duration);

        if ($now < $meetingStart) {
            $this->status = 'scheduled';
        } elseif ($now >= $meetingStart && $now <= $meetingEnd) {
            $this->status = 'started';
        } else {
            $this->status = 'ended';
        }

        $this->save();
    }
}
