<!doctype html>
<html class="no-js" lang="en">


<!-- Mirrored from skillgro.websolutionus.com/courses by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:37:53 GMT -->
<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Professional CPD Courses || Diagnostic Intelligence</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Discover professional CPD courses in clinical chemistry, hematology, microbiology and more. Advance your career with expert-led training and industry-recognized certifications.">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Custom Meta -->
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="uploads/website-images/favicon.png">
    <!-- CSS here -->
    <link rel="stylesheet" href="frontend/css/bootstrap.min.css">
    <link rel="stylesheet" href="frontend/css/animate.min.css">
    <link rel="stylesheet" href="frontend/css/magnific-popup.css">
    <link rel="stylesheet" href="frontend/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="frontend/css/flaticon-skillgro.css">
    <link rel="stylesheet" href="frontend/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="frontend/css/default-icons.css">
    <link rel="stylesheet" href="frontend/css/select2.min.css">
    <link rel="stylesheet" href="frontend/css/odometer.css">
    <link rel="stylesheet" href="frontend/css/aos.css">
    <link rel="stylesheet" href="frontend/css/plyr.css">
    <link rel="stylesheet" href="frontend/css/spacing.css">
    <link rel="stylesheet" href="frontend/css/tg-cursor.css">
    <link rel="stylesheet" href="frontend/css/bootstrap-datepicker.min.css">
    <link rel="stylesheet" href="global/toastr/toastr.min.css">
    <link rel="stylesheet" href="global/nice-select/nice-select.css">
    <link rel="stylesheet" href="frontend/css/main.minc669.css?v=2.5.0">
    <link rel="stylesheet" href="frontend/css/frontend.minc669.css?v=2.5.0">



    <style>
        :root {
            --tg-theme-primary: #5751e1;
            --tg-theme-secondary: #ffc224;
            --tg-common-color-blue: #050071;
            --tg-common-color-blue-2: #282568;
            --tg-common-color-dark: #1c1a4a;
            --tg-common-color-black: #06042e;
            --tg-common-color-dark-2: #4a44d1;
        }
        
        /* Enhanced Courses Page Styles */
        .courses-hero-area {
            position: relative;
            overflow: hidden;
        }
        
        .hero-search-wrap {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .search-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 8px;
            backdrop-filter: blur(10px);
        }
        
        .search-input-wrapper {
            position: relative;
            flex: 1;
        }
        
        .search-input {
            border: none;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            padding: 12px 20px 12px 45px;
            font-size: 16px;
        }
        
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            z-index: 2;
        }
        
        .search-btn {
            border-radius: 25px;
            padding: 12px 24px;
            font-weight: 600;
            white-space: nowrap;
        }
        
        .hero-stats {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 2rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .filter-tags-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .filter-tag {
            transition: all 0.3s ease;
        }
        
        .filter-tag:hover, .filter-tag.active {
            background-color: var(--tg-theme-primary);
            border-color: var(--tg-theme-primary);
            color: white;
        }
        
        .courses__sidebar {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .widget-title {
            display: flex;
            align-items: center;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--tg-common-color-dark);
        }
        
        .filter-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        
        .courses-header-section {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 25px;
        }
        
        .view-toggle {
            display: flex;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .view-toggle .btn {
            border-radius: 0;
            border-right: none;
        }
        
        .view-toggle .btn:last-child {
            border-right: 1px solid #dee2e6;
        }
        
        .view-toggle .btn.active {
            background-color: var(--tg-theme-primary);
            border-color: var(--tg-theme-primary);
            color: white;
        }
        
        .list-view .course-item {
            display: flex;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .list-view .course-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .pagination-container {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .no-results-state, .loading-state {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        @media (max-width: 768px) {
            .search-input-group {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }
            
            .search-btn {
                width: 100%;
            }
            
            .hero-stats .row {
                text-align: center;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>


    <script>
        "use strict";
        //write your javascript here without the script tag
    </script>

</head>

<body class="">


    <!-- Scroll-top -->
    <button class="scroll__top scroll-to-target" data-target="html" aria-label="Scroll Top">
        <i class="tg-flaticon-arrowhead-up"></i>
    </button>
    <!-- Scroll-top-end-->

    <!-- header-area -->
    <!-- header-area -->
  @include('includes.header')
    <!-- header-area-end -->
    <!-- header-area-end -->

    <!-- main-area -->
    <main class="main-area fix">
        <!-- hero-area -->
        <section class="courses-hero-area section-py-120" style="background: linear-gradient(135deg, #5751e1 0%, #3fdacf 100%);">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 text-center">
                        <div class="courses-hero-content text-white">
                            <h1 class="hero-title mb-4">Discover Professional CPD Courses</h1>
                            <p class="hero-desc mb-5">Advance your career with industry-recognized courses designed by experts. Learn at your own pace and earn valuable certifications.</p>
                            
                            <!-- Enhanced Search Bar -->
                            <div class="hero-search-wrap">
                                <form class="hero-search-form" action="#" method="GET">
                                    <div class="search-input-group">
                                        <div class="search-input-wrapper">
                                            <i class="fas fa-search search-icon"></i>
                                            <input type="text" class="form-control search-input" placeholder="Search for courses, topics, or skills..." name="search" id="course-search">
                                        </div>
                                        <button type="submit" class="btn btn-light search-btn">
                                            <i class="fas fa-search me-2"></i>Search Courses
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Quick Stats -->
                            <div class="hero-stats mt-5">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h3 class="stat-number">500+</h3>
                                            <p class="stat-label">Expert Courses</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h3 class="stat-number">25K+</h3>
                                            <p class="stat-label">Active Students</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h3 class="stat-number">98%</h3>
                                            <p class="stat-label">Success Rate</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- hero-area-end -->

        <!-- all-courses -->
        <section class="all-courses-area section-py-120 top-baseline">
            <div class="container position-relative">
                <div class="preloader-two d-none">
                    <div class="loader-icon-two"><img src="uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg"
                            alt="Preloader"></div>
                </div>
                <!-- Filter Tags -->
                <div class="filter-tags-section mb-4">
                    <div class="d-flex flex-wrap align-items-center gap-2">
                        <span class="filter-label">Popular:</span>
                        <button class="btn btn-outline-primary btn-sm filter-tag" data-filter="clinical-chemistry">Clinical Chemistry</button>
                        <button class="btn btn-outline-primary btn-sm filter-tag" data-filter="hematology">Hematology</button>
                        <button class="btn btn-outline-primary btn-sm filter-tag" data-filter="microbiology">Microbiology</button>
                        <button class="btn btn-outline-primary btn-sm filter-tag" data-filter="quality-management">Quality Management</button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-xl-3 col-lg-4">
                        <div class="courses__sidebar_area">
                            <div class="courses__sidebar_button d-lg-none">
                                <h4><i class="fas fa-filter me-2"></i>Filters</h4>
                            </div>
                            <aside class="courses__sidebar">
                                <!-- Clear Filters Button -->
                                <div class="filter-header mb-3">
                                    <button class="btn btn-outline-secondary btn-sm clear-filters" style="width: 100%;">
                                        <i class="fas fa-times me-2"></i>Clear All Filters
                                    </button>
                                </div>
                                
                                <div class="courses-widget">
                                    <h4 class="widget-title"><i class="fas fa-folder me-2"></i>Categories</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            @forelse($categories as $category)
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="{{ $category->slug }}" id="cat_{{ $category->id }}">
                                                    <label class="form-check-label" for="cat_{{ $category->id }}">
                                                        {{ $category->name }} ({{ $category->courses_count }})
                                                    </label>
                                                </div>
                                            </li>
                                            @empty
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="clinical-chemistry" id="cat_1">
                                                    <label class="form-check-label" for="cat_1">Clinical Chemistry (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="hematology" id="cat_2">
                                                    <label class="form-check-label" for="cat_2">Hematology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="microbiology" id="cat_3">
                                                    <label class="form-check-label" for="cat_3">Microbiology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="quality-management" id="cat_4">
                                                    <label class="form-check-label" for="cat_4">Quality Management (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="immunology" id="cat_5">
                                                    <label class="form-check-label" for="cat_5">Immunology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="laboratory-safety" id="cat_6">
                                                    <label class="form-check-label" for="cat_6">Laboratory Safety (1)</label>
                                                </div>
                                            </li>
                                            @endforelse
                                        </ul>
                                        <div class="show-more">
                                        </div>
                                    </div>
                                </div>

                                <div class="sub-category-holder "></div>
                                <div class="courses-widget">
                                    <h4 class="widget-title"><i class="fas fa-globe me-2"></i>Language</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="" id="lang">
                                                    <label class="form-check-label" for="lang">All Languages</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="English" id="lang_1">
                                                    <label class="form-check-label" for="lang_1">English</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="Swahili" id="lang_2">
                                                    <label class="form-check-label" for="lang_2">Swahili</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="show-more">
                                    </div>
                                </div>
                                <div class="courses-widget">
                                    <h4 class="widget-title"><i class="fas fa-dollar-sign me-2"></i>Price</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="" id="price_1">
                                                    <label class="form-check-label" for="price_1">All Price</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="free" id="price_2">
                                                    <label class="form-check-label" for="price_2">Free</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="paid" id="price_3">
                                                    <label class="form-check-label" for="price_3">Paid</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="courses-widget">
                                    <h4 class="widget-title"><i class="fas fa-chart-line me-2"></i>Skill Level</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="" id="difficulty_all">
                                                    <label class="form-check-label" for="difficulty_all">All
                                                        Levels</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="1" id="difficulty_1">
                                                    <label class="form-check-label" for="difficulty_1">Beginner</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="2" id="difficulty_2">
                                                    <label class="form-check-label"
                                                        for="difficulty_2">Intermediate</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="3" id="difficulty_3">
                                                    <label class="form-check-label" for="difficulty_3">Advanced</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </aside>
                        </div>
                    </div>
                    <div class="col-xl-9 col-lg-8">
                        <!-- Enhanced Course Header -->
                        <div class="courses-header-section mb-4">
                            <div class="courses-top-wrap">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="courses-top-left">
                                            <h5 class="mb-1">Available Courses</h5>
                                            <p class="text-muted mb-0">Total <span class="course-count fw-bold text-primary">0</span> courses found</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-end align-items-center gap-3">
                                            <!-- View Toggle -->
                                            <div class="view-toggle">
                                                <button class="btn btn-outline-secondary btn-sm active" data-view="grid">
                                                    <i class="fas fa-th"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" data-view="list">
                                                    <i class="fas fa-list"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- Sort Dropdown -->
                                            <div class="courses-top-right">
                                                <span class="sort-by me-2">Sort:</span>
                                                <div class="courses-top-right-select">
                                                    <select name="orderby" class="orderby form-select form-select-sm">
                                                        <option value="desc">Latest First</option>
                                                        <option value="asc">Oldest First</option>
                                                        <option value="popular">Most Popular</option>
                                                        <option value="rating">Highest Rated</option>
                                                        <option value="price_low">Price: Low to High</option>
                                                        <option value="price_high">Price: High to Low</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Loading State -->
                        <div class="loading-state text-center py-5 d-none">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted">Loading courses...</p>
                        </div>
                        
                        <!-- No Results State -->
                        <div class="no-results-state text-center py-5 d-none">
                            <div class="no-results-icon mb-3">
                                <i class="fas fa-search" style="font-size: 4rem; color: #ddd;"></i>
                            </div>
                            <h4 class="text-muted mb-3">No Courses Found</h4>
                            <p class="text-muted mb-4">Try adjusting your filters or search terms to find what you're looking for.</p>
                            <button class="btn btn-primary clear-filters">Clear All Filters</button>
                        </div>
                        
                        <!-- Course Content -->
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="grid" role="tabpanel" aria-labelledby="grid-tab">
                                <!-- Grid View -->
                                <div class="course-holder row courses__grid-wrap row-cols-1 row-cols-xl-3 row-cols-lg-2 row-cols-md-2 row-cols-sm-1 grid-view">
                                    <!-- Courses will be loaded here -->
                                </div>
                                
                                <!-- List View -->
                                <div class="course-holder-list list-view d-none">
                                    <!-- List view courses will be loaded here -->
                                </div>

                                <!-- Enhanced Pagination -->
                                <div class="pagination-wrap mt-5">
                                    <div class="pagination-container">
                                        <div class="pagination-info text-center mb-3">
                                            <span class="text-muted">Showing <span class="current-range">1-12</span> of <span class="total-courses">0</span> courses</span>
                                        </div>
                                        <div class="pagination d-flex justify-content-center">
                                            <!-- Pagination will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- all-courses-end -->
    </main>
    <!-- main-area-end -->

    <!-- modal-area -->
    <!-- Modal -->
    <div class="modal fade dynamic-modal modal-lg" tabindex="-1" aria-labelledby="dynamic-modalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="d-flex justify-content-center align-items:center p-3">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade bd-example-modal-lg" id="iframeModal" data-bs-backdrop="static" tabindex="-1"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <h5>Your are using this website under an external iframe</h5>
                    <p>For a better experience please browse directly instead of an external iframe</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <a target="_blank" href="index.html" class="btn btn-sm btn-primary">Browse Directly</a>
                </div>
            </div>
        </div>
    </div> <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Chapter Title</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="https://skillgro.websolutionus.com/instructor/course-chapter//store"
                        class="instructor__profile-form" method="post">
                        <input type="hidden" name="_token" value="LjkuUqlaSDW4X740h7HW6tT5rFW58frkItXzEuOO"
                            autocomplete="off">
                        <div class="col-md-12">
                            <div class="form-grp">
                                <label for="title">Title <code>*</code></label>
                                <input id="title" name="title" type="text" value="">
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Create</button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal-area -->

    <!-- footer-area -->
 @include('includes.footer')
    <!-- footer-area-end -->


    <!-- JS here -->
    <script src="global/js/jquery-3.7.1.min.js"></script>
    <script src="frontend/js/proper.min.js"></script>
    <script src="frontend/js/bootstrap.min.js"></script>
    <script src="frontend/js/imagesloaded.pkgd.min.js"></script>
    <script src="frontend/js/jquery.magnific-popup.min.js"></script>
    <script src="frontend/js/jquery.odometer.min.js"></script>
    <script src="frontend/js/jquery.appear.js"></script>
    <script src="frontend/js/tween-max.min.js"></script>
    <script src="frontend/js/select2.min.js"></script>
    <script src="frontend/js/swiper-bundle.min.js"></script>
    <script src="frontend/js/jquery.marquee.min.js"></script>
    <script src="frontend/js/tg-cursor.min.js"></script>
    <script src="frontend/js/svg-inject.min.js"></script>
    <script src="frontend/js/jquery.circleType.js"></script>
    <script src="frontend/js/jquery.lettering.min.js"></script>
    <script src="frontend/js/bootstrap-datepicker.min.js"></script>
    <script src="frontend/js/plyr.min.js"></script>
    <script src="frontend/js/wow.min.js"></script>
    <script src="frontend/js/aos.js"></script>
    <script src="frontend/js/vivus.min.js"></script>
    <script src="global/toastr/toastr.min.js"></script>
    <script src="frontend/js/sweetalert.js"></script>
    <script src="frontend/js/default/frontendc669.js?v=2.5.0"></script>
    <script src="frontend/js/default/cartc669.js?v=2.5.0"></script>
    <script src="global/nice-select/jquery.nice-select.min.js"></script>
    <!-- File Manager js-->
    <script src="vendor/laravel-filemanager/js/stand-alone-button.js"></script>


    <script src="frontend/js/mainc669.js?v=2.5.0"></script>

    <script>
        $('.file-manager').filemanager('file', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });
    $('.file-manager-image').filemanager('image', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });

    SVGInject(document.querySelectorAll("img.injectable"));
    </script>

    <!-- dynamic Toastr Notification -->
    <script>
        "use strict";
    toastr.options.closeButton = true;
    toastr.options.progressBar = true;
    toastr.options.positionClass = 'toast-bottom-right';


    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        orientation: "bottom auto"
    });
    </script>


    <!-- Toastr -->


    <!-- Google reCAPTCHA -->

    <!-- tawk -->

    <!-- Cookie Consent -->
    <script src="frontend/js/cookieconsent.min.js"></script>

    <script>
        "use strict";
        window.addEventListener("load", function() {
            window.wpcc.init({
                "border": "normal",
                "corners": "none",
                "colors": {
                    "popup": {
                        "background": "#5751e1",
                        "text": "#fafafa !important",
                        "border": "#5751e1"
                    },
                    "button": {
                        "background": "#fffceb",
                        "text": "#222758"
                    }
                },
                "content": {
                    "href": "https://skillgro.websolutionus.com/page/privacy-policy",
                    "message": "This website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only upon approval.",
                    "link": "More Info",
                    "button": "Yes"
                }
            })
        });
    </script>

    <script>
        if ($(".marquee_mode").length) {
        $('.marquee_mode').marquee({
            speed: 20,
            gap: 35,
            delayBeforeStart: 0,
            direction: "left",
            duplicated: true,
            pauseOnHover: true,
            startVisible: true,
        });
    }
    </script>

    <script>
        $(document).on("click", '.wpcc-btn', function() {
        $('.wpcc-container').fadeOut(1000);
    });
    </script>

    <!-- Language Translation Variables -->
    <script>
        var base_url = "{{ url('/') }}";
  var preloader_path = "uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg";

  var demo_mode_error = "This Is Demo Version. You Can Not Change Anything";
  var translation_success = "Translated Successfully!";
  var translation_processing = "Translation Processing, please wait...";
  var search_instructor_placeholder = "Search for an instructor with email or name";
  var Previous = "Previous";
  var Next = "Next";
  var basic_error_message = "Something went wrong";
  var discount = "Discount";
  var subscribe_now = "Subscribe Now";
  var submitting = "Submitting...";
  var submitting = "Submitting...";
  var login_first = "Login first";
    </script>
    <!-- Page specific js -->
    <script src="frontend/js/default/course-page.js"></script>
    <script>
        "use strict";
        
        // Enhanced Course Page Functionality
        $(document).ready(function() {
            // Filter tags functionality
            $('.filter-tag').on('click', function() {
                $('.filter-tag').removeClass('active');
                $(this).addClass('active');
                
                // Trigger filter based on data-filter attribute
                const filterValue = $(this).data('filter');
                // Add your filter logic here
                console.log('Filtering by:', filterValue);
            });
            
            // View toggle functionality
            $('.view-toggle .btn').on('click', function() {
                const view = $(this).data('view');
                $('.view-toggle .btn').removeClass('active');
                $(this).addClass('active');
                
                if (view === 'grid') {
                    $('.grid-view').removeClass('d-none');
                    $('.list-view').addClass('d-none');
                } else {
                    $('.grid-view').addClass('d-none');
                    $('.list-view').removeClass('d-none');
                }
            });
            
            // Clear filters functionality
            $('.clear-filters').on('click', function() {
                // Reset all checkboxes and radio buttons
                $('.courses__sidebar input[type="checkbox"]').prop('checked', false);
                $('.courses__sidebar input[type="radio"]').prop('checked', false);
                $('.filter-tag').removeClass('active');
                
                // Reset search
                $('#course-search').val('');
                
                // Trigger course reload
                loadCourses();
            });
            
            // Enhanced search functionality
            let searchTimeout;
            $('#course-search').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                
                searchTimeout = setTimeout(function() {
                    if (searchTerm.length >= 2 || searchTerm.length === 0) {
                        loadCourses();
                    }
                }, 500);
            });
            
            // Loading states
            function showLoading() {
                $('.loading-state').removeClass('d-none');
                $('.course-holder, .course-holder-list').addClass('d-none');
                $('.no-results-state').addClass('d-none');
            }
            
            function hideLoading() {
                $('.loading-state').addClass('d-none');
                $('.course-holder, .course-holder-list').removeClass('d-none');
            }
            
            function showNoResults() {
                $('.no-results-state').removeClass('d-none');
                $('.course-holder, .course-holder-list').addClass('d-none');
                $('.loading-state').addClass('d-none');
            }
            
            // Mock function for loading courses (replace with actual AJAX call)
            function loadCourses() {
                showLoading();
                
                // Simulate API call
                setTimeout(function() {
                    hideLoading();
                    // Update course count
                    $('.course-count').text('24');
                    $('.total-courses').text('24');
                }, 1000);
            }
            
            // Smooth scrolling for filter application
            function scrollToResults() {
                $('html, body').animate({
                    scrollTop: $('.courses-header-section').offset().top - 100
                }, 500);
            }
            
            // Filter change handlers
            $('.courses__sidebar input').on('change', function() {
                loadCourses();
                scrollToResults();
            });
            
            // Sort change handler
            $('.orderby').on('change', function() {
                loadCourses();
            });
            
            // Initialize tooltips if Bootstrap is available
            if (typeof bootstrap !== 'undefined') {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        });
    </script>
</body>




</html>
