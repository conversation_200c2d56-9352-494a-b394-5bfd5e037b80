<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\ZoomService;
use App\Models\Course;
use App\Models\ZoomMeeting;
use Illuminate\Support\Facades\Log;

echo "=== Zoom API Debug Test ===\n\n";

// 1. Check environment variables
echo "1. Environment Variables:\n";
echo "ZOOM_ACCOUNT_ID: " . (env('ZOOM_ACCOUNT_ID') ? 'Set' : 'Not set') . "\n";
echo "ZOOM_CLIENT_ID: " . (env('ZOOM_CLIENT_ID') ? 'Set' : 'Not set') . "\n";
echo "ZOOM_CLIENT_SECRET: " . (env('ZOOM_CLIENT_SECRET') ? 'Set' : 'Not set') . "\n\n";

// 2. Check config values
echo "2. Config Values:\n";
echo "services.zoom.account_id: " . (config('services.zoom.account_id') ? 'Set' : 'Not set') . "\n";
echo "services.zoom.client_id: " . (config('services.zoom.client_id') ? 'Set' : 'Not set') . "\n";
echo "services.zoom.client_secret: " . (config('services.zoom.client_secret') ? 'Set' : 'Not set') . "\n\n";

// 3. Check existing courses
echo "3. Database Status:\n";
echo "Total courses: " . Course::count() . "\n";
echo "Courses with live sessions: " . Course::where('has_live_session', true)->count() . "\n";
echo "Total Zoom meetings: " . ZoomMeeting::count() . "\n\n";

// 4. Test ZoomService instantiation
echo "4. ZoomService Test:\n";
try {
    $zoomService = new ZoomService();
    echo "✓ ZoomService instantiated successfully\n";
    
    // Test access token retrieval
    echo "Testing access token retrieval...\n";
    $reflection = new ReflectionClass($zoomService);
    $method = $reflection->getMethod('getAccessToken');
    $method->setAccessible(true);
    
    $token = $method->invoke($zoomService);
    echo "✓ Access token retrieved: " . substr($token, 0, 20) . "...\n";
    
} catch (Exception $e) {
    echo "✗ Error with ZoomService: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
