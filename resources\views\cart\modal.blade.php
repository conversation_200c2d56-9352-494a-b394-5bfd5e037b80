<div class="cart-modal-content">
    @if(empty($cartItems))
        <div class="cart-empty text-center py-4">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5>Your cart is empty</h5>
            <p class="text-muted">Add some courses to get started!</p>
            <a href="{{ route('visitors.courses') }}" class="btn btn-primary btn-sm">Browse Courses</a>
        </div>
    @else
        <div class="cart-items">
            @foreach($cartItems as $item)
                <div class="cart-item d-flex align-items-center mb-3 pb-3 border-bottom" data-course-id="{{ $item['id'] }}">
                    <div class="cart-item-thumb me-3">
                        @if($item['thumbnail'])
                            <img src="{{ asset('storage/' . $item['thumbnail']) }}" alt="{{ $item['title'] }}" 
                                 class="rounded" style="width: 60px; height: 45px; object-fit: cover;">
                        @else
                            <img src="{{ asset('frontend/img/course/course_thumb01.jpg') }}" alt="{{ $item['title'] }}" 
                                 class="rounded" style="width: 60px; height: 45px; object-fit: cover;">
                        @endif
                    </div>
                    <div class="cart-item-content flex-grow-1">
                        <h6 class="cart-item-title mb-1">
                            <a href="{{ route('course.show', $item['slug']) }}" class="text-decoration-none">
                                {{ Str::limit($item['title'], 40) }}
                            </a>
                        </h6>
                        <small class="text-muted">By {{ $item['instructor'] }}</small>
                        <div class="cart-item-price mt-1">
                            @if($item['is_free'])
                                <span class="text-success fw-bold">Free</span>
                            @else
                                <span class="text-primary fw-bold">KES {{ number_format($item['price'], 2) }}</span>
                            @endif
                            @if($item['quantity'] > 1)
                                <small class="text-muted"> x {{ $item['quantity'] }}</small>
                            @endif
                        </div>
                    </div>
                    <div class="cart-item-actions">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-item-modal" 
                                data-course-id="{{ $item['id'] }}" title="Remove from cart">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="cart-summary mt-3 pt-3 border-top">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">Total Courses:</span>
                <span class="badge bg-primary">{{ $cartSummary['item_count'] }}</span>
            </div>
            
            @if($cartSummary['free_courses'] > 0)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Free Courses:</span>
                    <span class="text-success">{{ $cartSummary['free_courses'] }}</span>
                </div>
            @endif
            
            @if($cartSummary['paid_courses'] > 0)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Paid Courses:</span>
                    <span>{{ $cartSummary['paid_courses'] }}</span>
                </div>
            @endif

            <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="fw-bold fs-5">Total:</span>
                <span class="fw-bold fs-5 text-primary">{{ $cartSummary['total_formatted'] }}</span>
            </div>

            <div class="cart-actions d-grid gap-2">
                <a href="{{ route('cart.index') }}" class="btn btn-outline-primary">View Cart</a>
                @if($cartSummary['total'] > 0)
                    <button type="button" class="btn btn-primary proceed-checkout-modal">
                        Proceed to Checkout
                    </button>
                @else
                    <button type="button" class="btn btn-success" disabled>
                        All courses are free
                    </button>
                @endif
            </div>
        </div>
    @endif
</div>

<style>
.cart-modal-content {
    max-height: 400px;
    overflow-y: auto;
}

.cart-item-thumb img {
    transition: transform 0.2s;
}

.cart-item-thumb img:hover {
    transform: scale(1.05);
}

.cart-item-title a {
    color: #333;
    font-size: 14px;
    line-height: 1.3;
}

.cart-item-title a:hover {
    color: #007bff;
}

.cart-item-price {
    font-size: 13px;
}

.remove-item-modal {
    padding: 0.25rem 0.5rem;
    font-size: 12px;
}

.cart-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.cart-actions .btn {
    font-size: 14px;
    padding: 8px 16px;
}

.cart-empty i {
    opacity: 0.5;
}
</style>
