<?php

// Simple script to populate homepage settings
require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'database' => env('DB_DATABASE', 'lernovate'),
    'username' => env('DB_USERNAME', 'root'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

// Create site_settings table if it doesn't exist
if (!Capsule::schema()->hasTable('site_settings')) {
    Capsule::schema()->create('site_settings', function (Blueprint $table) {
        $table->id();
        $table->string('key')->unique();
        $table->longText('value')->nullable();
        $table->string('type')->default('text');
        $table->string('group')->default('general');
        $table->text('description')->nullable();
        $table->integer('sort_order')->default(0);
        $table->timestamps();
        
        $table->index(['group', 'sort_order']);
        $table->index('key');
    });
}

// Homepage settings data
$settings = [
    // Hero Section
    ['key' => 'hero_title', 'value' => 'Master Professional Skills with CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 1],
    ['key' => 'hero_description', 'value' => 'Transform your career with industry-recognized CPD courses. Learn from expert instructors, earn valuable certifications, and advance your professional development at your own pace.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 2],
    ['key' => 'hero_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 3],
    ['key' => 'hero_button_text', 'value' => 'Explore Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 4],
    
    // About Section
    ['key' => 'about_subtitle', 'value' => 'Get To Know About Us', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 10],
    ['key' => 'about_title', 'value' => 'Empowering Minds, Shaping Futures', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 11],
    ['key' => 'about_description', 'value' => 'We\'re committed to providing high-quality, accessible professional development that fits your schedule. Our platform combines cutting-edge technology with expert instruction to deliver an unparalleled learning experience.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 12],
    ['key' => 'about_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 13],
    ['key' => 'about_button_text', 'value' => 'Start Free Trial', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 14],
    
    // CTA Section
    ['key' => 'cta_title', 'value' => 'Ready to Start Your Learning Journey?', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 40],
    ['key' => 'cta_description', 'value' => 'Join thousands of professionals who have advanced their careers with our CPD programs. Start learning today and unlock your potential!', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 41],
    ['key' => 'cta_button1_text', 'value' => 'Browse Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 42],
    ['key' => 'cta_button2_text', 'value' => 'Learn More', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 43],
    
    // Colors
    ['key' => 'primary_color', 'value' => '#5751e1', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 100],
    ['key' => 'secondary_color', 'value' => '#3fdacf', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 101],
    ['key' => 'accent_color', 'value' => '#ffc224', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 102],
    
    // Stats
    ['key' => 'stats_students', 'value' => '25K', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 110],
    ['key' => 'stats_cpd_programs', 'value' => '10', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 111],
    ['key' => 'stats_satisfaction', 'value' => '99%', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 112],
];

// Insert settings
foreach ($settings as $setting) {
    $existing = Capsule::table('site_settings')->where('key', $setting['key'])->first();
    if (!$existing) {
        Capsule::table('site_settings')->insert(array_merge($setting, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }
}

echo "Homepage settings have been populated successfully!\n";
echo "You can now access the admin panel at: http://127.0.0.1:8000/admin/homepage\n";
echo "And view the homepage at: http://127.0.0.1:8000/\n";
?>