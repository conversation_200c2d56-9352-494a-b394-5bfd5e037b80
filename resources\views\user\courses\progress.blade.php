@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Course Progress</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Course Progress</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                @forelse($enrollments as $enrollment)
                <div class="col-xl-6 col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-md me-3">
                                    @if($enrollment->course->thumbnail)
                                        <img src="{{ asset('storage/' . $enrollment->course->thumbnail) }}" alt="" class="img-fluid rounded">
                                    @else
                                        <div class="avatar-title rounded bg-primary">
                                            {{ substr($enrollment->course->title, 0, 1) }}
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h5 class="font-size-15 mb-1">{{ $enrollment->course->title }}</h5>
                                    <p class="text-muted mb-0">{{ $enrollment->course->category->name ?? 'General' }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    @if($enrollment->isCompleted())
                                        <span class="badge badge-soft-success">Completed</span>
                                    @else
                                        <span class="badge badge-soft-primary">In Progress</span>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">Progress</span>
                                    <span class="text-muted">{{ $enrollment->progress_percentage }}%</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar {{ $enrollment->isCompleted() ? 'bg-success' : 'bg-primary' }}" 
                                         role="progressbar" 
                                         style="width: {{ $enrollment->progress_percentage }}%" 
                                         aria-valuenow="{{ $enrollment->progress_percentage }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                            </div>

                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="mt-0">
                                        <p class="text-muted mb-1">Enrolled</p>
                                        <h5 class="font-size-14 mb-0">{{ $enrollment->enrolled_at->format('M d, Y') }}</h5>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mt-0">
                                        <p class="text-muted mb-1">Last Access</p>
                                        <h5 class="font-size-14 mb-0">
                                            {{ $enrollment->last_accessed_at ? $enrollment->last_accessed_at->format('M d, Y') : 'Never' }}
                                        </h5>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mt-0">
                                        <p class="text-muted mb-1">Status</p>
                                        <h5 class="font-size-14 mb-0">
                                            @if($enrollment->isCompleted())
                                                <span class="text-success">Complete</span>
                                            @else
                                                <span class="text-primary">Active</span>
                                            @endif
                                        </h5>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 d-flex gap-2">
                                @if($enrollment->isCompleted())
                                    <a href="{{ route('course.show', $enrollment->course->slug) }}" class="btn btn-outline-primary btn-sm">Review Course</a>
                                    @if($enrollment->course->certificate)
                                        <a href="{{ route('user.certificates') }}" class="btn btn-success btn-sm">View Certificate</a>
                                    @endif
                                @else
                                    <a href="{{ route('course.show', $enrollment->course->slug) }}" class="btn btn-primary btn-sm">Continue Learning</a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <div class="avatar-md mx-auto mb-4">
                                <div class="avatar-title rounded-circle bg-primary bg-soft text-primary">
                                    <i class="uil-chart-line font-size-24"></i>
                                </div>
                            </div>
                            <h5 class="font-size-16">No Course Progress</h5>
                            <p class="text-muted">You haven't enrolled in any courses yet. Start learning to track your progress.</p>
                            <a href="{{ route('visitors.courses') }}" class="btn btn-primary">Browse Courses</a>
                        </div>
                    </div>
                </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection