# Zoom Meeting Integration Setup Guide

## Overview
This integration allows admins to schedule Zoom meetings when creating courses, track student attendance (minimum 45 minutes), and provide recordings to enrolled students.

## Features Implemented

### 1. Database Structure
- **zoom_meetings table**: Stores Zoom meeting details
- **meeting_attendances table**: Tracks user attendance and duration
- **courses table**: Added meeting-related fields

### 2. Models Created
- **ZoomMeeting**: Manages Zoom meeting data and relationships
- **MeetingAttendance**: Tracks user attendance records
- **Course**: Extended with meeting relationships and methods

### 3. Services
- **ZoomService**: Handles all Zoom API interactions
  - Create/update/delete meetings
  - Process webhooks
  - Sync attendance data
  - Manage recordings

### 4. Controllers
- **CourseController**: Extended to handle meeting creation during course setup
- **MeetingController**: Manages user and admin meeting interactions

### 5. Routes Added
- User meeting routes (view, join, recordings)
- Admin meeting management routes
- Zoom webhook endpoint

## Setup Instructions

### 1. Zoom App Configuration
1. Go to [Zoom Marketplace](https://marketplace.zoom.us/)
2. Create a new JWT App (or Server-to-Server OAuth App)
3. Get your API Key and API Secret
4. Set up webhook endpoints

### 2. Environment Configuration
Add these variables to your `.env` file:

```env
# Zoom API Configuration
ZOOM_API_KEY=your_zoom_api_key_here
ZOOM_API_SECRET=your_zoom_api_secret_here
ZOOM_WEBHOOK_SECRET=your_webhook_secret_here
```

### 3. Webhook Setup
Configure your Zoom app to send webhooks to:
```
https://yourdomain.com/zoom/webhook
```

Enable these webhook events:
- meeting.participant_joined
- meeting.participant_left
- meeting.started
- meeting.ended
- recording.completed

### 4. Database Migration
The migrations have been run and the following tables are created:
- `zoom_meetings`
- `meeting_attendances`
- Added fields to `courses` table

## Usage Guide

### For Admins

#### Creating a Course with Live Session
1. Go to Admin → Courses → Create New Course
2. Fill in course details
3. Check "Has Live Session" checkbox
4. Fill in meeting details:
   - Meeting Topic
   - Meeting Description
   - Start Date & Time
   - Duration (minutes)
   - Password (optional)
   - Minimum Attendance Minutes (default: 45)
   - Check "Required for Completion" if attendance is mandatory

#### Managing Meetings
- View all meetings: `/admin/meetings`
- View meeting details and attendance: `/admin/meetings/{meeting}`
- Sync attendance from Zoom: Click "Sync Attendance" button
- Update recording URL manually if needed

### For Students

#### Accessing Meetings
1. Enroll in a course with live sessions
2. Go to User Dashboard → Meetings
3. View upcoming and active meetings
4. Join meetings when available (15 minutes before start time)

#### Meeting Requirements
- Must be enrolled in the course
- Can join 15 minutes before meeting starts
- Must stay for minimum 45 minutes (or configured duration)
- Attendance is tracked automatically via Zoom webhooks

#### Accessing Recordings
- Recordings become available after the meeting ends
- Access via course page or meetings section
- Only available to enrolled students

## Technical Details

### Attendance Tracking
- Automatic via Zoom webhooks
- Manual sync available for admins
- Tracks join/leave times
- Calculates total duration
- Marks as "qualified" if minimum time met

### Course Completion
- If meeting attendance is required for completion
- System checks if user attended minimum duration
- Integrates with existing course completion logic

### Security
- JWT tokens for Zoom API authentication
- Webhook signature verification
- User enrollment verification for meeting access
- Role-based access control

## API Endpoints

### User Endpoints
- `GET /user/meetings` - List user's meetings
- `GET /user/meetings/{meeting}` - Meeting details
- `GET /user/meetings/{meeting}/join` - Join meeting (redirects to Zoom)
- `GET /user/meetings/{meeting}/recordings` - Access recordings

### Admin Endpoints
- `GET /admin/meetings` - List all meetings
- `GET /admin/meetings/{meeting}` - Meeting details with attendance
- `POST /admin/meetings/{meeting}/sync-attendance` - Sync attendance
- `POST /admin/meetings/{meeting}/update-status` - Update meeting status
- `POST /admin/meetings/{meeting}/update-recording` - Update recording URL

### Webhook Endpoint
- `POST /zoom/webhook` - Zoom webhook handler

## Troubleshooting

### Common Issues
1. **Meeting creation fails**: Check Zoom API credentials
2. **Attendance not tracking**: Verify webhook configuration
3. **Users can't join**: Check enrollment and meeting timing
4. **Recordings not available**: Ensure cloud recording is enabled

### Logs
Check Laravel logs for Zoom API errors:
```bash
tail -f storage/logs/laravel.log
```

### Testing
1. Create a test course with live session
2. Schedule a meeting for immediate testing
3. Join with test user account
4. Verify attendance tracking
5. Check recording availability

## Future Enhancements
- Automatic email notifications for meetings
- Meeting reminders
- Breakout room support
- Advanced attendance reports
- Integration with certificates based on attendance
