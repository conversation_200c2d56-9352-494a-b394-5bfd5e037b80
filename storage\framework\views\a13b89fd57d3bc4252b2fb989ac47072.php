

<?php $__env->startSection('title', 'Payment Status'); ?>

<?php $__env->startSection('content'); ?>
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="payment-status-card">
                        <div class="text-center mb-4">
                            <div class="payment-status-icon" id="status-icon">
                                <?php if($transaction->transaction_status === 'pending'): ?>
                                    <div class="spinner-border text-warning" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                <?php elseif($transaction->transaction_status === 'paid'): ?>
                                    <i class="fas fa-check-circle text-success fa-4x"></i>
                                <?php elseif($transaction->transaction_status === 'failed'): ?>
                                    <i class="fas fa-times-circle text-danger fa-4x"></i>
                                <?php endif; ?>
                            </div>
                            
                            <h2 class="payment-status-title mt-3" id="status-title">
                                <?php if($transaction->transaction_status === 'pending'): ?>
                                    Processing Payment...
                                <?php elseif($transaction->transaction_status === 'paid'): ?>
                                    Payment Successful!
                                <?php elseif($transaction->transaction_status === 'failed'): ?>
                                    Payment Failed
                                <?php endif; ?>
                            </h2>
                            
                            <p class="payment-status-message" id="status-message">
                                <?php if($transaction->transaction_status === 'pending'): ?>
                                    Please wait while we confirm your M-Pesa payment. This usually takes a few seconds.
                                <?php elseif($transaction->transaction_status === 'paid'): ?>
                                    Your payment has been confirmed and you have been enrolled in your courses.
                                <?php elseif($transaction->transaction_status === 'failed'): ?>
                                    Your payment could not be processed. Please try again.
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="payment-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Transaction ID:</span>
                                        <span class="value"><?php echo e($transaction->transaction_request_id); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Amount:</span>
                                        <span class="value">KES <?php echo e(number_format($transaction->amount, 2)); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Phone Number:</span>
                                        <span class="value"><?php echo e($transaction->phone_number); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Status:</span>
                                        <span class="value status-badge" id="status-badge">
                                            <?php if($transaction->transaction_status === 'pending'): ?>
                                                <span class="badge bg-warning">Processing</span>
                                            <?php elseif($transaction->transaction_status === 'paid'): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php elseif($transaction->transaction_status === 'failed'): ?>
                                                <span class="badge bg-danger">Failed</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if($transaction->transaction_status === 'pending'): ?>
                            <div class="payment-progress mt-4">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" 
                                         role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <p class="text-center mt-2 text-muted">
                                    <small>Checking payment status... <span id="countdown">60</span>s</small>
                                </p>
                            </div>
                        <?php endif; ?>

                        <div class="payment-actions mt-4 text-center">
                            <?php if($transaction->transaction_status === 'paid'): ?>
                                <a href="<?php echo e(route('user.dashboard')); ?>" class="btn btn-success btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                                </a>
                            <?php elseif($transaction->transaction_status === 'failed'): ?>
                                <a href="<?php echo e(route('cart.index')); ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-redo me-2"></i>Try Again
                                </a>
                            <?php else: ?>
                                <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                                    <i class="fas fa-sync me-2"></i>Refresh Status
                                </button>
                            <?php endif; ?>
                        </div>
        </div>
    </div>
</div>

<style>
.payment-status-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.payment-status-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.payment-status-icon {
    margin-bottom: 20px;
}

.payment-status-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.payment-status-message {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
}

.payment-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin: 30px 0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.detail-item .label {
    font-weight: 600;
    color: #495057;
}

.detail-item .value {
    color: #333;
    font-weight: 500;
}

.payment-progress {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.progress {
    height: 8px;
    border-radius: 10px;
}

.payment-actions .btn {
    min-width: 200px;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
}

@media (max-width: 768px) {
    .payment-status-card {
        padding: 25px;
        margin: 15px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

<?php if($transaction->transaction_status === 'pending'): ?>
<script>
let countdown = 60;
let checkInterval;

function updateCountdown() {
    document.getElementById('countdown').textContent = countdown;
    countdown--;
    
    if (countdown < 0) {
        clearInterval(checkInterval);
        window.location.reload();
    }
}

function checkPaymentStatus() {
    fetch(`<?php echo e(route('payment.check', $transaction->transaction_request_id)); ?>`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                if (data.transaction_status === 'paid') {
                    // Payment successful - redirect to dashboard
                    window.location.href = data.redirect_url;
                } else if (data.transaction_status === 'failed') {
                    // Payment failed - redirect to cart
                    window.location.href = data.redirect_url;
                }
                // If still pending, continue checking
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
        });
}

// Start checking payment status every 5 seconds
setInterval(checkPaymentStatus, 5000);

// Start countdown
checkInterval = setInterval(updateCountdown, 1000);

// Initial check
checkPaymentStatus();
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.payment', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/payment/status.blade.php ENDPATH**/ ?>