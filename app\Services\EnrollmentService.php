<?php

namespace App\Services;

use App\Models\Enrollment;
use App\Models\User;
use App\Models\Course;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class EnrollmentService
{
    /**
     * Create enrollments for a user based on course IDs
     *
     * @param int $userId
     * @param array $courseIds
     * @return array
     */
    public function createEnrollments(int $userId, array $courseIds): array
    {
        try {
            DB::beginTransaction();

            $user = User::find($userId);
            if (!$user) {
                throw new \Exception("User with ID {$userId} not found");
            }

            $enrolledCourses = [];
            $skippedCourses = [];

            foreach ($courseIds as $courseId) {
                $course = Course::find($courseId);
                
                if (!$course) {
                    Log::warning("Course with ID {$courseId} not found during enrollment");
                    $skippedCourses[] = $courseId;
                    continue;
                }

                // Check if user is already enrolled in this course
                $existingEnrollment = Enrollment::where('user_id', $userId)
                    ->where('course_id', $courseId)
                    ->first();

                if ($existingEnrollment) {
                    Log::info("User {$userId} already enrolled in course {$courseId}");
                    $skippedCourses[] = $courseId;
                    continue;
                }

                // Create new enrollment
                $enrollment = Enrollment::create([
                    'user_id' => $userId,
                    'course_id' => $courseId,
                    'enrolled_at' => now(),
                    'progress' => 0,
                    'status' => 'enrolled',
                    'last_accessed_at' => null,
                ]);

                $enrolledCourses[] = [
                    'enrollment_id' => $enrollment->id,
                    'course_id' => $courseId,
                    'course_title' => $course->title,
                ];

                Log::info("Successfully enrolled user {$userId} in course {$courseId}", [
                    'enrollment_id' => $enrollment->id,
                    'course_title' => $course->title,
                ]);
            }

            DB::commit();

            return [
                'status' => 'success',
                'enrolled_courses' => $enrolledCourses,
                'skipped_courses' => $skippedCourses,
                'message' => count($enrolledCourses) . ' course(s) enrolled successfully',
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Enrollment creation failed', [
                'user_id' => $userId,
                'course_ids' => $courseIds,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => 'error',
                'message' => 'Failed to create enrollments: ' . $e->getMessage(),
                'enrolled_courses' => [],
                'skipped_courses' => $courseIds,
            ];
        }
    }

    /**
     * Check if user is enrolled in a course
     *
     * @param int $userId
     * @param int $courseId
     * @return bool
     */
    public function isUserEnrolled(int $userId, int $courseId): bool
    {
        return Enrollment::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->exists();
    }

    /**
     * Get user's enrollment for a specific course
     *
     * @param int $userId
     * @param int $courseId
     * @return Enrollment|null
     */
    public function getUserEnrollment(int $userId, int $courseId): ?Enrollment
    {
        return Enrollment::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->first();
    }

    /**
     * Update enrollment progress
     *
     * @param int $enrollmentId
     * @param float $progress
     * @return bool
     */
    public function updateProgress(int $enrollmentId, float $progress): bool
    {
        try {
            $enrollment = Enrollment::find($enrollmentId);
            
            if (!$enrollment) {
                return false;
            }

            $enrollment->update([
                'progress' => $progress,
                'last_accessed_at' => now(),
                'status' => $progress >= 100 ? 'completed' : 'in_progress',
                'completed_at' => $progress >= 100 ? now() : null,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update enrollment progress', [
                'enrollment_id' => $enrollmentId,
                'progress' => $progress,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
