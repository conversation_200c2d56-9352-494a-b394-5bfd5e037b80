@extends('welcome')

@section('content')
<main class="main-area fix">
    <!-- breadcrumb-area -->
    <section class="breadcrumb__area breadcrumb__bg" data-background="{{ asset('frontend/img/bg/breadcrumb_bg.jpg') }}">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="breadcrumb__content">
                        <h3 class="title">Order Successful</h3>
                        <nav class="breadcrumb">
                            <span property="itemListElement" typeof="ListItem">
                                <a href="{{ url('/') }}">Home</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">Order Success</span>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <!-- success-area -->
    <section class="success__area section-py-120">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="success__wrapper">
                        <div class="success__icon">
                            <i class="fas fa-check-circle"></i>
                        </div>

                        <div class="success__content">
                            <h2 class="success__title">Order Placed Successfully!</h2>
                            <p class="success__message">
                                Thank you for your purchase! Your order has been received and
                                @if($order->payment_status === 'completed')
                                    payment has been confirmed.
                                @elseif($order->payment_status === 'pending')
                                    is being processed.
                                @elseif($order->payment_status === 'pending_verification')
                                    is pending payment verification.
                                @endif
                            </p>

                            <div class="order__details">
                                <div class="order__info">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info__item">
                                                <span class="label">Order Number:</span>
                                                <span class="value">{{ $order->order_number }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info__item">
                                                <span class="label">Order Date:</span>
                                                <span class="value">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info__item">
                                                <span class="label">Payment Method:</span>
                                                <span class="value">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info__item">
                                                <span class="label">Total Amount:</span>
                                                <span class="value">{{ $order->formatted_total }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if($order->payment_status === 'completed' && $order->mpesa_receipt_number)
                                <div class="payment__confirmation">
                                    <h5>Payment Confirmation</h5>
                                    <div class="confirmation__details">
                                        <div class="detail__item">
                                            <span class="label">M-Pesa Receipt:</span>
                                            <span class="value">{{ $order->mpesa_receipt_number }}</span>
                                        </div>
                                        <div class="detail__item">
                                            <span class="label">Payment Date:</span>
                                            <span class="value">{{ $order->paid_at->format('M d, Y \a\t g:i A') }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <div class="order__items">
                                    <h5>Purchased Courses</h5>
                                    <div class="items__list">
                                        @foreach($order->orderItems as $item)
                                        <div class="order__item">
                                            <div class="item__content">
                                                <h6 class="item__title">{{ $item->course_title }}</h6>
                                                <div class="item__details">
                                                    <span class="item__price">{{ $item->formatted_price }}</span>
                                                    <span class="item__qty">× {{ $item->quantity }}</span>
                                                    <span class="item__total">{{ $item->formatted_total }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>

                                @if($order->payment_status === 'pending_verification')
                                <div class="payment__instructions">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> Payment Instructions</h6>
                                        <p>Please complete your bank transfer using the following details:</p>
                                        <ul>
                                            <li><strong>Bank:</strong> Equity Bank Kenya</li>
                                            <li><strong>Account Name:</strong> SkillGro Learning Platform</li>
                                            <li><strong>Account Number:</strong> **********</li>
                                            <li><strong>Reference:</strong> {{ $order->order_number }}</li>
                                        </ul>
                                        <p class="mb-0">After completing the transfer, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> with your transaction details.</p>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <div class="success__actions">
                                @if($order->payment_status === 'completed')
                                <a href="{{ route('user.dashboard') }}" class="btn btn-two">
                                    <i class="fas fa-play-circle"></i> Start Learning
                                </a>
                                @endif
                                <a href="{{ route('visitors.courses') }}" class="btn btn-outline">
                                    <i class="fas fa-book"></i> Browse More Courses
                                </a>
                                <a href="{{ url('/') }}" class="btn btn-outline">
                                    <i class="fas fa-home"></i> Back to Home
                                </a>
                            </div>

                            @if($order->payment_status === 'completed')
                            <div class="next__steps">
                                <h5>What's Next?</h5>
                                <div class="steps__list">
                                    <div class="step__item">
                                        <div class="step__icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="step__content">
                                            <h6>Check Your Email</h6>
                                            <p>We've sent you a confirmation email with your course access details.</p>
                                        </div>
                                    </div>
                                    <div class="step__item">
                                        <div class="step__icon">
                                            <i class="fas fa-user-graduate"></i>
                                        </div>
                                        <div class="step__content">
                                            <h6>Access Your Courses</h6>
                                            <p>Go to your dashboard to start learning immediately.</p>
                                        </div>
                                    </div>
                                    <div class="step__item">
                                        <div class="step__icon">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                        <div class="step__content">
                                            <h6>Earn Certificates</h6>
                                            <p>Complete courses to earn certificates and boost your career.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- success-area-end -->
</main>

<style>
.success__wrapper {
    background: #fff;
    padding: 60px 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.success__icon {
    margin-bottom: 30px;
}

.success__icon i {
    font-size: 80px;
    color: #28a745;
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.success__title {
    color: #28a745;
    margin-bottom: 20px;
    font-size: 32px;
    font-weight: 700;
}

.success__message {
    font-size: 18px;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.order__details {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 40px;
    text-align: left;
}

.order__info {
    margin-bottom: 30px;
}

.info__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.info__item:last-child {
    border-bottom: none;
}

.info__item .label {
    font-weight: 600;
    color: #495057;
}

.info__item .value {
    color: #212529;
    font-weight: 500;
}

.payment__confirmation {
    background: #d4edda;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin-bottom: 30px;
}

.payment__confirmation h5 {
    color: #155724;
    margin-bottom: 15px;
}

.confirmation__details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail__item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail__item .label {
    font-size: 12px;
    color: #155724;
    font-weight: 600;
    text-transform: uppercase;
}

.detail__item .value {
    color: #155724;
    font-weight: 500;
}

.order__items h5 {
    margin-bottom: 20px;
    color: #495057;
}

.order__item {
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.order__item:last-child {
    border-bottom: none;
}

.item__title {
    margin-bottom: 8px;
    color: #212529;
}

.item__details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.item__price {
    color: var(--tg-theme-primary);
    font-weight: 600;
}

.item__qty {
    color: #6c757d;
}

.item__total {
    color: #212529;
    font-weight: 600;
}

.payment__instructions {
    margin-top: 30px;
}

.payment__instructions .alert {
    text-align: left;
}

.payment__instructions ul {
    margin: 15px 0;
    padding-left: 20px;
}

.success__actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.success__actions .btn {
    min-width: 160px;
}

.next__steps {
    text-align: left;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
}

.next__steps h5 {
    text-align: center;
    margin-bottom: 30px;
    color: #495057;
}

.steps__list {
    display: grid;
    gap: 25px;
}

.step__item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.step__icon {
    width: 50px;
    height: 50px;
    background: var(--tg-theme-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.step__icon i {
    color: #fff;
    font-size: 20px;
}

.step__content h6 {
    margin-bottom: 8px;
    color: #212529;
}

.step__content p {
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .success__wrapper {
        padding: 40px 20px;
    }

    .success__title {
        font-size: 24px;
    }

    .success__message {
        font-size: 16px;
    }

    .confirmation__details {
        grid-template-columns: 1fr;
    }

    .success__actions {
        flex-direction: column;
        align-items: center;
    }

    .success__actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
@endsection
