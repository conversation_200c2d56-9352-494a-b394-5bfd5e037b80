"use strict";

const csrf_token = $("meta[name='csrf-token']").attr("content");

/** On Document Load */

$(document).ready(function () {

    // Initialize cart counter on page load
    updateCartCounter();

    // Add to cart
    $(document).on("click", ".add-to-cart", function (e) {
        e.preventDefault();
        let element = $(this);
        let courseId = $(this).data('id');
        let courseTitle = $(this).data('title') || 'Course';

        // Debug logging
        console.log('Add to cart clicked:', {
            courseId: courseId,
            courseTitle: courseTitle,
            base_url: typeof base_url !== 'undefined' ? base_url : 'UNDEFINED',
            csrf_token: typeof csrf_token !== 'undefined' ? csrf_token : 'UNDEFINED'
        });

        // Check if required variables are defined
        if (typeof base_url === 'undefined') {
            toastr.error('Base URL not defined. Please refresh the page.');
            return;
        }

        if (typeof csrf_token === 'undefined') {
            toastr.error('CSRF token not found. Please refresh the page.');
            return;
        }

        if (!courseId) {
            toastr.error('Course ID not found. Please try again.');
            return;
        }

        $.ajax({
            method: "post",
            url: base_url + "/add-to-cart/" + courseId,
            data: {
                _token: csrf_token
            },
            beforeSend: function () {
                element.find("span").text("Adding...");
                element.prop('disabled', true);
            },
            success: function (data) {
                console.log('Cart add response:', data);
                if (data.status == "success") {
                    toastr.success(data.message);
                    updateCartCounter();

                    // Analytics tracking
                    if (data.dataLayer && typeof data.dataLayer === 'object') {
                        dataLayer.push({
                            'event': 'addToCart',
                            'cart_details': data.dataLayer
                        });
                    }
                } else if (data.status == "info") {
                    toastr.info(data.message);
                    updateCartCounter();
                } else {
                    toastr.error(data.message);
                }
            },
            error: function (xhr, status, error) {
                console.error('Cart add error:', {
                    xhr: xhr,
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    toastr.error(xhr.responseJSON.message);
                } else {
                    toastr.error("Failed to add course to cart. Please try again.");
                }
            },
            complete: function () {
                element.find("span").text("Add To Cart");
                element.prop('disabled', false);
            }
        });
    });

    // Remove item from cart
    $(document).on("click", ".remove-item, .remove-item-modal", function (e) {
        e.preventDefault();
        let courseId = $(this).data('course-id');
        let row = $(this).closest('tr, .cart-item');

        if (confirm('Are you sure you want to remove this course from your cart?')) {
            $.ajax({
                method: "DELETE",
                url: base_url + "/cart/remove/" + courseId,
                data: {
                    _token: csrf_token
                },
                beforeSend: function () {
                    row.addClass('removing');
                },
                success: function (data) {
                    if (data.status == "success") {
                        toastr.success(data.message);
                        row.fadeOut(300, function() {
                            $(this).remove();
                            updateCartDisplay();
                        });
                        updateCartCounter();
                    } else {
                        toastr.error(data.message);
                        row.removeClass('removing');
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("Failed to remove course from cart.");
                    row.removeClass('removing');
                }
            });
        }
    });

    // Clear entire cart
    $(document).on("click", ".clear-cart", function (e) {
        e.preventDefault();

        if (confirm('Are you sure you want to clear your entire cart?')) {
            $.ajax({
                method: "DELETE",
                url: base_url + "/cart/clear",
                data: {
                    _token: csrf_token
                },
                beforeSend: function () {
                    $('.clear-cart').prop('disabled', true).text('Clearing...');
                },
                success: function (data) {
                    if (data.status == "success") {
                        toastr.success(data.message);
                        location.reload(); // Reload to show empty cart state
                    } else {
                        toastr.error(data.message);
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("Failed to clear cart.");
                },
                complete: function () {
                    $('.clear-cart').prop('disabled', false).text('Clear Cart');
                }
            });
        }
    });

    // apply coupon
    $('.coupon-form').on('submit', function (e) {
        e.preventDefault();

        let formData = $(this).serialize();
        $.ajax({
            method: "POST",
            url: base_url + "/apply-coupon",
            data: formData,
            beforeSend: function () {
                $('.coupon-form button').attr('disabled', true);
                $('.coupon-form button').text("Applying...");
            },
            success: function (data) {
                let html = `
                  <span>${discount}</span>
                    <br>
                  <small>${data.coupon_code} (${data.offer_percentage}%) <a class="ms-2 text-danger" href="/remove-coupon">×</a></small>
                `;
                $('.coupon-discount').html(html);
                $('.discount-amount').text(data.discount_amount);
                $('.amount').text(data.total);
                // reset form
                $('.coupon-form button').attr('disabled', false);
                $('.coupon-form button').text("Apply Coupon");
                $('.coupon-form')[0].reset();
                toastr.success(data.message);
            },
            error: function (xhr, status, error) {
                $('.coupon-form button').attr('disabled', false);
                $('.coupon-form button').text("Apply Coupon");
                if (xhr.responseJSON?.errors) {
                    $.each(xhr.responseJSON.errors, function (key, value) {
                        toastr.error(value);
                    });
                } else if (xhr.responseJSON?.message) {
                    toastr.error(xhr.responseJSON.message);
                }
            }
        })
    });

    // Cart icon click to show modal
    $(document).on("click", "#cart-icon", function (e) {
        if ($(window).width() > 768) { // Only show modal on desktop
            e.preventDefault();
            showCartModal();
        }
        // On mobile, let it navigate to cart page
    });

    // Update quantity in cart
    $(document).on("click", ".qtybutton", function () {
        let $button = $(this);
        let $input = $button.parent().find("input");
        let courseId = $button.closest('tr').data('course-id');
        let oldValue = parseInt($input.val()) || 1;
        let newVal;

        if ($button.text() == "+") {
            newVal = oldValue + 1;
        } else {
            newVal = Math.max(0, oldValue - 1);
        }

        if (newVal !== oldValue && courseId) {
            updateCartQuantity(courseId, newVal);
        }
        $input.val(newVal);
    });

    // Proceed to checkout
    $(document).on("click", ".proceed-checkout, .proceed-checkout-modal", function (e) {
        e.preventDefault();
        toastr.info("Checkout functionality will be implemented soon!");
        // TODO: Implement checkout process
    });
});

// Helper Functions
function updateCartCounter() {
    console.log('Updating cart counter...');
    $.ajax({
        method: "GET",
        url: base_url + "/cart/count",
        success: function (data) {
            console.log('Cart count response:', data);
            if (data.status == "success") {
                $('.mini-cart-count, #cart-counter').text(data.cart_count);
            }
        },
        error: function (xhr, status, error) {
            console.error('Cart count error:', {xhr, status, error});
            $('.mini-cart-count, #cart-counter').text('0');
        }
    });
}

function updateCartQuantity(courseId, quantity) {
    $.ajax({
        method: "PATCH",
        url: base_url + "/cart/update/" + courseId,
        data: {
            _token: csrf_token,
            quantity: quantity
        },
        success: function (data) {
            if (data.status == "success") {
                toastr.success(data.message);
                updateCartCounter();
                updateCartDisplay();

                // Update item total in the row
                let row = $('tr[data-course-id="' + courseId + '"]');
                if (row.length && data.item_total) {
                    row.find('.item-total').text(data.item_total);
                }
            } else {
                toastr.error(data.message);
            }
        },
        error: function () {
            toastr.error("Failed to update cart.");
        }
    });
}

function updateCartDisplay() {
    // Update cart totals and summary
    $.ajax({
        method: "GET",
        url: base_url + "/cart/get",
        success: function (data) {
            if (data.status == "success") {
                $('#total-courses').text(data.summary.item_count);
                $('#cart-subtotal').text(data.summary.subtotal_formatted);
                $('#cart-total').text(data.summary.total_formatted);

                // Check if cart is empty and reload if needed
                if (data.summary.item_count === 0) {
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            }
        }
    });
}

function showCartModal() {
    $.ajax({
        method: "GET",
        url: base_url + "/cart/modal",
        beforeSend: function () {
            $('#cartModal').modal('show');
            $('#cart-modal-body').html(`
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `);
        },
        success: function (data) {
            if (data.status == "success") {
                $('#cart-modal-body').html(data.html);
            } else {
                $('#cart-modal-body').html(`
                    <div class="text-center py-4">
                        <p class="text-danger">Failed to load cart contents.</p>
                    </div>
                `);
            }
        },
        error: function () {
            $('#cart-modal-body').html(`
                <div class="text-center py-4">
                    <p class="text-danger">Failed to load cart contents.</p>
                </div>
            `);
        }
    });
}

// Validate cart on page load for cart page
if (window.location.pathname.includes('/cart')) {
    $(document).ready(function () {
        $.ajax({
            method: "GET",
            url: base_url + "/cart/validate",
            success: function (data) {
                if (data.status == "warning") {
                    toastr.warning(data.message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            }
        });
    });
}
