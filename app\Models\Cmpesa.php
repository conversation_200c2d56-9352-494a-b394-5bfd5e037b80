<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cmpesa extends Model
{
    protected $fillable = [
        'transaction_request_id',
        'phone_number',
        'billing_name',
        'amount',
        'billing_email',
        'billing_phone',
        'payment_method',
        'account_reference',
        'transaction_status',
        'user_id',
        'course_ids',
        'enrollment_created',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'course_ids' => 'array',
        'enrollment_created' => 'boolean',
    ];

    /**
     * Get the user that owns the payment transaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if payment is completed/paid
     */
    public function isPaid(): bool
    {
        return $this->transaction_status === 'paid';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->transaction_status === 'pending';
    }

    /**
     * Check if payment failed
     */
    public function isFailed(): bool
    {
        return $this->transaction_status === 'failed';
    }

    /**
     * Get the course IDs as an array
     */
    public function getCourseIdsAttribute($value): array
    {
        if (is_string($value)) {
            return json_decode($value, true) ?? [];
        }
        
        return $value ?? [];
    }

    /**
     * Scope for paid transactions
     */
    public function scopePaid($query)
    {
        return $query->where('transaction_status', 'paid');
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('transaction_status', 'pending');
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('transaction_status', 'failed');
    }

    /**
     * Scope for transactions where enrollment has not been created
     */
    public function scopeEnrollmentNotCreated($query)
    {
        return $query->where('enrollment_created', false);
    }
}
