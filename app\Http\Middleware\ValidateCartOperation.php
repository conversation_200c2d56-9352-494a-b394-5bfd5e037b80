<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\CartService;

class ValidateCartOperation
{
    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Rate limiting for cart operations
        $key = 'cart_operations:' . $request->ip();
        $maxAttempts = 60; // 60 operations per minute
        $decayMinutes = 1;

        if (cache()->has($key)) {
            $attempts = cache()->get($key);
            if ($attempts >= $maxAttempts) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Too many cart operations. Please try again later.',
                ], 429);
            }
            cache()->put($key, $attempts + 1, now()->addMinutes($decayMinutes));
        } else {
            cache()->put($key, 1, now()->addMinutes($decayMinutes));
        }

        // Validate cart integrity before operations
        if ($request->isMethod('post') || $request->isMethod('patch') || $request->isMethod('delete')) {
            $validation = $this->cartService->validateCart();
            if ($validation['status'] === 'warning') {
                // Log cart validation issues
                \Log::warning('Cart validation warning', [
                    'message' => $validation['message'],
                    'removed_items' => $validation['removed_items'] ?? [],
                    'user_ip' => $request->ip(),
                ]);
            }
        }

        return $next($request);
    }
}
