<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingsSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            // Header Settings
            ['key' => 'site_name', 'value' => 'Diagnostic Intelligence', 'type' => 'text', 'group' => 'header', 'sort_order' => 1, 'description' => 'Website name displayed in header'],
            ['key' => 'logo', 'value' => 'assets/images/logo.png', 'type' => 'image', 'group' => 'header', 'sort_order' => 2, 'description' => 'Main website logo'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'email', 'group' => 'header', 'sort_order' => 3, 'description' => 'Contact email displayed in header'],
            ['key' => 'contact_phone', 'value' => '+254 700 000 000', 'type' => 'tel', 'group' => 'header', 'sort_order' => 4, 'description' => 'Contact phone number'],
            
            // Hero Section
            ['key' => 'hero_title', 'value' => 'Master Professional Skills with CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 1, 'description' => 'Main hero title'],
            ['key' => 'hero_highlight_text', 'value' => 'CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 2, 'description' => 'Highlighted text in hero title'],
            ['key' => 'hero_description', 'value' => 'Transform your career with industry-recognized CPD courses. Learn from expert instructors, earn valuable certifications, and advance your professional development at your own pace.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 3, 'description' => 'Hero section description'],
            ['key' => 'hero_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 4, 'description' => 'Hero section main image'],
            ['key' => 'hero_button_text', 'value' => 'Explore Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 5, 'description' => 'Hero section button text'],
            ['key' => 'hero_feature1', 'value' => 'Accredited Certificates', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 6, 'description' => 'First feature in hero section'],
            ['key' => 'hero_feature2', 'value' => 'Flexible Learning', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 7, 'description' => 'Second feature in hero section'],
            ['key' => 'hero_video_url', 'value' => '', 'type' => 'url', 'group' => 'homepage', 'sort_order' => 8, 'description' => 'YouTube video URL for hero section'],
            
            // About Section
            ['key' => 'about_subtitle', 'value' => 'Get To Know About Us', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 10, 'description' => 'About section subtitle'],
            ['key' => 'about_title', 'value' => 'Empowering Minds,', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 11, 'description' => 'About section title'],
            ['key' => 'about_highlight_text', 'value' => 'Shaping', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 12, 'description' => 'Highlighted text in about title'],
            ['key' => 'about_title_end', 'value' => 'Futures', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 13, 'description' => 'End part of about title'],
            ['key' => 'about_description', 'value' => 'We are committed to providing high-quality, accessible professional development that fits your schedule. Our platform combines cutting-edge technology with expert instruction to deliver an unparalleled learning experience.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 14, 'description' => 'About section description'],
            ['key' => 'about_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 15, 'description' => 'About section image'],
            ['key' => 'about_button_text', 'value' => 'Start Free Trial', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 16, 'description' => 'About section button text'],
            ['key' => 'about_feature1', 'value' => 'Industry-Leading Expert Instructors', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 17, 'description' => 'First about feature'],
            ['key' => 'about_feature2', 'value' => 'Learn Anywhere, Anytime', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 18, 'description' => 'Second about feature'],
            ['key' => 'about_feature3', 'value' => 'Personalized Learning Paths', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 19, 'description' => 'Third about feature'],
            ['key' => 'about_feature4', 'value' => 'Accredited CPD Certificates', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 20, 'description' => 'Fourth about feature'],
            ['key' => 'about_students_count', 'value' => '36K+', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 21, 'description' => 'Students count in about section'],
            ['key' => 'about_students_text', 'value' => 'Enrolled Students', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 22, 'description' => 'Students text in about section'],
            
            // CTA Section
            ['key' => 'cta_title', 'value' => 'Ready to Start Your Learning Journey?', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 40, 'description' => 'Call to action title'],
            ['key' => 'cta_description', 'value' => 'Join thousands of professionals who have advanced their careers with our CPD programs. Start learning today and unlock your potential!', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 41, 'description' => 'Call to action description'],
            ['key' => 'cta_button1_text', 'value' => 'Browse Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 42, 'description' => 'First CTA button text'],
            ['key' => 'cta_button2_text', 'value' => 'Learn More', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 43, 'description' => 'Second CTA button text'],
            
            // Statistics
            ['key' => 'stats_students', 'value' => '25K', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 50, 'description' => 'Students count statistic'],
            ['key' => 'stats_students_text', 'value' => 'Active Students', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 51, 'description' => 'Students statistic label'],
            ['key' => 'stats_cpd_programs', 'value' => '10', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 52, 'description' => 'CPD programs count'],
            ['key' => 'stats_cpd_text', 'value' => 'CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 53, 'description' => 'CPD programs label'],
            ['key' => 'stats_satisfaction', 'value' => '99%', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 54, 'description' => 'Satisfaction rate'],
            ['key' => 'stats_satisfaction_text', 'value' => 'Satisfaction Rate', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 55, 'description' => 'Satisfaction rate label'],
            ['key' => 'stats_courses_text', 'value' => 'Total Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 56, 'description' => 'Courses statistic label'],
            
            // Colors
            ['key' => 'primary_color', 'value' => '#5751e1', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 100, 'description' => 'Primary brand color'],
            ['key' => 'secondary_color', 'value' => '#3fdacf', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 101, 'description' => 'Secondary brand color'],
            ['key' => 'accent_color', 'value' => '#ffc224', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 102, 'description' => 'Accent color'],
            
            // Footer Settings
            ['key' => 'footer_text', 'value' => 'Empowering healthcare professionals with cutting-edge diagnostic education and training.', 'type' => 'textarea', 'group' => 'footer', 'sort_order' => 1, 'description' => 'Footer description text'],
            ['key' => 'copyright_text', 'value' => '© 2025 Diagnostic Intelligence. All rights reserved.', 'type' => 'text', 'group' => 'footer', 'sort_order' => 2, 'description' => 'Copyright text'],
            ['key' => 'footer_logo', 'value' => '', 'type' => 'image', 'group' => 'footer', 'sort_order' => 3, 'description' => 'Footer logo (optional)'],
            ['key' => 'social_facebook', 'value' => 'https://www.facebook.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 4, 'description' => 'Facebook page URL'],
            ['key' => 'social_linkedin', 'value' => 'https://www.linkedin.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 5, 'description' => 'LinkedIn page URL'],
            ['key' => 'social_youtube', 'value' => 'https://www.youtube.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 6, 'description' => 'YouTube channel URL'],
            ['key' => 'social_whatsapp', 'value' => 'https://web.whatsapp.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 7, 'description' => 'WhatsApp contact URL'],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}