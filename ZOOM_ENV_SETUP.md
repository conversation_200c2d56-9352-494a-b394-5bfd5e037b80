# Zoom Environment Configuration

Add these environment variables to your `.env` file:

```env
# Zoom OAuth 2.0 Credentials (Server-to-Server App)
ZOOM_ACCOUNT_ID=Gwg4nBS0RRiVPKlzGyCptw
ZOOM_CLIENT_ID=xZkpOjh3R7SkKgKUwyj9vQ
ZOOM_CLIENT_SECRET=FIzRu6xBybAcHW2swJ5sAd91I1ybT4a5

# Optional: Webhook Secret (for secure webhook verification)
ZOOM_WEBHOOK_SECRET=your_webhook_secret_here

# Legacy JWT Credentials (deprecated - for backward compatibility)
# ZOOM_API_KEY=your_jwt_api_key
# ZOOM_API_SECRET=your_jwt_api_secret
```

## Configuration Files Updated

1. `config/services.php` - Added OAuth 2.0 credentials support
2. `config/zoom.php` - Updated to use new credential format

## Usage

The ZoomService will automatically use the OAuth 2.0 credentials for API authentication. The legacy JWT credentials are kept for backward compatibility but are deprecated by Zoom.

## Next Steps

1. Add the environment variables to your `.env` file
2. Test the Zoom integration by creating a meeting through the admin panel
3. Verify webhook functionality if using real-time attendance tracking
