<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Certificate;
use App\Models\Assessment;
use Carbon\Carbon;

class UserDashboardSeeder extends Seeder
{
    public function run(): void
    {
        // Find or create a test user
        $user = User::where('email', '<EMAIL>')->first();
        if (!$user) {
            $user = User::create([
                'name' => 'Test Student',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'user',
                'email_verified_at' => now(),
            ]);
        }

        // Get some courses to enroll in
        $courses = Course::published()->take(5)->get();

        foreach ($courses as $index => $course) {
            // Create enrollment
            $enrollment = Enrollment::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'enrolled_at' => Carbon::now()->subDays(rand(1, 30)),
                'progress' => rand(20, 100),
                'status' => rand(0, 1) ? 'in_progress' : 'completed',
                'last_accessed_at' => Carbon::now()->subDays(rand(0, 7)),
            ]);

            // If completed, set completion date
            if ($enrollment->status === 'completed') {
                $enrollment->update([
                    'completed_at' => Carbon::now()->subDays(rand(1, 10)),
                    'progress' => 100,
                ]);

                // Create certificate if course offers one
                if ($course->certificate) {
                    Certificate::create([
                        'user_id' => $user->id,
                        'course_id' => $course->id,
                        'certificate_number' => 'CERT-' . strtoupper(uniqid()),
                        'issued_at' => $enrollment->completed_at,
                        'status' => 'active',
                    ]);
                }
            }

            // Create some assessments
            for ($i = 1; $i <= rand(1, 3); $i++) {
                $score = rand(60, 100);
                $maxScore = 100;
                
                Assessment::create([
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                    'title' => "Quiz {$i} - " . $course->title,
                    'type' => ['quiz', 'exam', 'assignment'][rand(0, 2)],
                    'score' => $score,
                    'max_score' => $maxScore,
                    'passed' => $score >= 70,
                    'completed_at' => Carbon::now()->subDays(rand(1, 20)),
                    'attempts' => rand(1, 3),
                ]);
            }
        }
    }
}