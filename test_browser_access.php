<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Admin\CourseController;
use App\Models\User;

echo "=== Testing Browser Access Simulation ===\n\n";

// 1. Test unauthenticated access
echo "1. Testing unauthenticated access:\n";
try {
    $request = Request::create('/admin-courses-create', 'GET');
    $controller = new CourseController(new \App\Services\ZoomService());
    $response = $controller->create();
    echo "✗ Unauthenticated access allowed (this shouldn't happen)\n";
} catch (\Exception $e) {
    echo "✓ Unauthenticated access properly blocked\n";
}

// 2. Test authenticated admin access
echo "\n2. Testing authenticated admin access:\n";
$user = User::where('role', 'admin')->first();
if ($user) {
    Auth::login($user);
    echo "✓ Admin user authenticated: {$user->name} (Role: {$user->role})\n";
    
    try {
        $request = Request::create('/admin-courses-create', 'GET');
        $controller = new CourseController(new \App\Services\ZoomService());
        $response = $controller->create();
        
        if ($response instanceof \Illuminate\View\View) {
            echo "✓ Course creation form loaded successfully\n";
            echo "View name: {$response->getName()}\n";
            
            // Check if required data is passed to view
            $viewData = $response->getData();
            echo "Categories count: " . (isset($viewData['categories']) ? $viewData['categories']->count() : 'Not set') . "\n";
            echo "Instructors count: " . (isset($viewData['instructors']) ? $viewData['instructors']->count() : 'Not set') . "\n";
        } else {
            echo "✗ Unexpected response type: " . get_class($response) . "\n";
        }
    } catch (\Exception $e) {
        echo "✗ Error accessing form: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ No admin user found\n";
}

// 3. Test form submission with missing CSRF token
echo "\n3. Testing form submission without CSRF token:\n";
$formData = [
    'title' => 'Test Course Without CSRF',
    'category_id' => 1,
    'instructor_id' => 1,
    'status' => 'draft',
    'has_live_session' => '1',
    'meeting_start_date' => date('Y-m-d', strtotime('+7 days')),
    'meeting_start_time' => '16:00',
];

try {
    $request = Request::create('/admin-courses-store', 'POST', $formData);
    $controller = new CourseController(new \App\Services\ZoomService());
    $response = $controller->store($request);
    echo "✗ Form submission without CSRF token succeeded (this shouldn't happen)\n";
} catch (\Exception $e) {
    if (strpos($e->getMessage(), 'CSRF') !== false || strpos($e->getMessage(), '419') !== false) {
        echo "✓ CSRF protection working correctly\n";
    } else {
        echo "✗ Different error: " . $e->getMessage() . "\n";
    }
}

// 4. Test form submission with CSRF token
echo "\n4. Testing form submission with CSRF token:\n";
$formDataWithCSRF = array_merge($formData, [
    '_token' => csrf_token(),
    'title' => 'Test Course With CSRF - ' . date('H:i:s'),
]);

try {
    $request = Request::create('/admin-courses-store', 'POST', $formDataWithCSRF);
    $controller = new CourseController(new \App\Services\ZoomService());
    $response = $controller->store($request);
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        echo "✓ Form submission with CSRF token succeeded\n";
        echo "Redirect URL: " . $response->getTargetUrl() . "\n";
    } else {
        echo "✗ Unexpected response type: " . get_class($response) . "\n";
    }
} catch (\Exception $e) {
    echo "✗ Error with CSRF token: " . $e->getMessage() . "\n";
}

echo "\n=== Browser Access Test Complete ===\n";
