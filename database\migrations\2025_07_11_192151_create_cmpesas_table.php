<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cmpesas', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_request_id');
            $table->string('phone_number');
            $table->string('billing_name');
            $table->decimal('amount', 10, 2);
            $table->string('billing_email');
            $table->string('billing_phone');
            $table->string('payment_method');
            $table->string('account_reference');
            $table->string('transaction_status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cmpesas');
    }
};
