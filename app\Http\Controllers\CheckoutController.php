<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\CartService;
use App\Services\MpesaService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CheckoutController extends Controller
{
    protected CartService $cartService;
    protected MpesaService $mpesaService;

    public function __construct(CartService $cartService, MpesaService $mpesaService)
    {
        $this->cartService = $cartService;
        $this->mpesaService = $mpesaService;
    }

    /**
     * Display the checkout page
     */
    public function index()
    {
        $cartItems = $this->cartService->getItems();
        $cartSummary = $this->cartService->getSummary();

        // Redirect if cart is empty
        if (empty($cartItems)) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        // Validate cart items
        $validation = $this->cartService->validateCart();
        if ($validation['status'] === 'error') {
            return redirect()->route('cart.index')->with('error', $validation['message']);
        }

        return view('checkout.index', compact('cartItems', 'cartSummary', 'validation'));
    }

    /**
     * Process checkout and create order
     */
    public function process(Request $request): JsonResponse
    {
        $request->validate([
            'payment_method' => 'required|in:mpesa,card,bank_transfer',
            'phone_number' => 'required_if:payment_method,mpesa|regex:/^254[0-9]{9}$/',
            'billing_name' => 'required|string|max:255',
            'billing_email' => 'required|email|max:255',
            'billing_phone' => 'required|string|max:20',
            'terms_accepted' => 'required|accepted',
        ]);

        try {
            DB::beginTransaction();

            $cartItems = $this->cartService->getItems();
            $cartSummary = $this->cartService->getSummary();

            if (empty($cartItems)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Your cart is empty.',
                ], 400);
            }

            // Create order
            $order = Order::create([
                'order_number' => $this->generateOrderNumber(),
                'user_id' => Auth::id(),
                'billing_name' => $request->billing_name,
                'billing_email' => $request->billing_email,
                'billing_phone' => $request->billing_phone,
                'payment_method' => $request->payment_method,
                'subtotal' => $cartSummary['subtotal'],
                'total' => $cartSummary['total'],
                'status' => 'completed',
                'payment_status' => 'completed',
            ]);

            // Create order items
            foreach ($cartItems as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'course_id' => $item['id'],
                    'course_title' => $item['title'],
                    'course_price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'total' => $item['price'] * $item['quantity'],
                ]);
            }

            // Simulate payment success without actual payment processing
            DB::commit();

            // Clear cart after successful order creation
            $this->cartService->clear();

            return response()->json([
                'status' => 'success',
                'message' => 'Order placed successfully without actual payment.',
                'order_id' => $order->id,
                'redirect_url' => route('checkout.success', $order->order_number),
                'payment_data' => null,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Checkout process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Checkout failed. Please try again.',
            ], 500);
        }
    }

    /**
     * Process payment based on selected method
     */
    private function processPayment(Order $order, Request $request): array
    {
        switch ($request->payment_method) {
            case 'mpesa':
                return $this->processMpesaPayment($order, $request->phone_number);

            case 'card':
                return $this->processCardPayment($order);

            case 'bank_transfer':
                return $this->processBankTransfer($order);

            default:
                return [
                    'status' => 'error',
                    'message' => 'Invalid payment method selected.',
                ];
        }
    }

    /**
     * Process M-Pesa payment
     */
    private function processMpesaPayment(Order $order, string $phoneNumber): array
    {
        try {
            $result = $this->mpesaService->initiateSTKPush([
                'phone_number' => $phoneNumber,
                'amount' => $order->total,
                'account_reference' => $order->order_number,
                'transaction_desc' => "Payment for Order #{$order->order_number}",
            ]);

            if ($result['status'] === 'success') {
                // Update order with M-Pesa transaction details
                $order->update([
                    'mpesa_checkout_request_id' => $result['checkout_request_id'],
                    'payment_reference' => $result['checkout_request_id'],
                ]);

                return [
                    'status' => 'success',
                    'message' => 'M-Pesa payment initiated. Please check your phone for the payment prompt.',
                    'data' => [
                        'checkout_request_id' => $result['checkout_request_id'],
                        'merchant_request_id' => $result['merchant_request_id'],
                    ],
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => $result['message'] ?? 'M-Pesa payment initiation failed.',
                ];
            }
        } catch (\Exception $e) {
            Log::error('M-Pesa payment failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'M-Pesa payment failed. Please try again.',
            ];
        }
    }

    /**
     * Process card payment (placeholder for future implementation)
     */
    private function processCardPayment(Order $order): array
    {
        // Placeholder for future card payment implementation
        $order->update([
            'payment_status' => 'pending',
            'status' => 'pending_payment',
        ]);

        return [
            'status' => 'success',
            'message' => 'Card payment processing is not yet implemented. Please use M-Pesa or bank transfer.',
        ];
    }

    /**
     * Process bank transfer (manual verification)
     */
    private function processBankTransfer(Order $order): array
    {
        $order->update([
            'payment_status' => 'pending_verification',
            'status' => 'pending_payment',
        ]);

        return [
            'status' => 'success',
            'message' => 'Order created successfully. Please complete the bank transfer and contact us for verification.',
        ];
    }

    /**
     * Handle M-Pesa callback
     */
    public function mpesaCallback(Request $request): JsonResponse
    {
        try {
            $result = $this->mpesaService->handleCallback($request->all());

            if ($result['status'] === 'success') {
                $order = Order::where('mpesa_checkout_request_id', $result['checkout_request_id'])->first();

                if ($order) {
                    $order->update([
                        'payment_status' => 'completed',
                        'status' => 'completed',
                        'mpesa_receipt_number' => $result['mpesa_receipt_number'],
                        'paid_at' => now(),
                    ]);

                    // Enroll user in courses
                    $this->enrollUserInCourses($order);
                }
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('M-Pesa callback failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Request $request): JsonResponse
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
        ]);

        try {
            $order = Order::findOrFail($request->order_id);

            return response()->json([
                'status' => 'success',
                'payment_status' => $order->payment_status,
                'order_status' => $order->status,
                'message' => $this->getPaymentStatusMessage($order),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to check payment status.',
            ], 500);
        }
    }

    /**
     * Success page
     */
    public function success(string $orderNumber): View
    {
        $order = Order::where('order_number', $orderNumber)->firstOrFail();
        return view('checkout.success', compact('order'));
    }

    /**
     * Failed page
     */
    public function failed(): View
    {
        return view('checkout.failed');
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Enroll user in purchased courses
     */
    private function enrollUserInCourses(Order $order): void
    {
        foreach ($order->orderItems as $item) {
            // Add enrollment logic here
            // This would typically create enrollment records
            Log::info('User enrolled in course', [
                'user_id' => $order->user_id,
                'course_id' => $item->course_id,
                'order_id' => $order->id,
            ]);
        }
    }

    /**
     * Get payment status message
     */
    private function getPaymentStatusMessage(Order $order): string
    {
        switch ($order->payment_status) {
            case 'pending':
                return 'Payment is being processed. Please wait...';
            case 'completed':
                return 'Payment completed successfully!';
            case 'failed':
                return 'Payment failed. Please try again.';
            case 'pending_verification':
                return 'Payment is pending verification. We will contact you soon.';
            default:
                return 'Unknown payment status.';
        }
    }
}
