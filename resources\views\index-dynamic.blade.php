@extends('welcome')
@section('content')
<main class="main-area fix">
    <!-- banner-area -->
    <section class="banner-area banner-bg tg-motion-effects" data-background="frontend/img/banner/banner_bg.png">
        <div class="container">
            <div class="row">
                <div class="col-xl-5 col-lg-6">
                    <div class="banner__content">
                        <h3 class="title tg-svg">
                            {{ \App\Models\SiteSetting::get('hero_title', 'Master Professional Skills with') }}
                            <span class="position-relative">
                                <span class="svg-icon" id="banner-svg">
                                    <svg x="0px" y="0px" preserveAspectRatio="none" viewBox="0 0 209 59" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="path" d="M4.74438 7.70565C69.7006 -1.18799 136.097 -2.38304 203.934 4.1205C207.178 4.48495 209.422 7.14626 208.933 10.0534C206.793 23.6481 205.415 36.5704 204.801 48.8204C204.756 51.3291 202.246 53.5582 199.213 53.7955C136.093 59.7623 74.1922 60.5985 13.5091 56.3043C10.5653 56.0924 7.84371 53.7277 7.42158 51.0325C5.20725 38.2627 2.76333 25.6511 0.0898448 13.1978C-0.465589 10.5873 1.61173 8.1379 4.74438 7.70565Z" stroke="url(#paint0_linear_47_27)" stroke-width="7" pathLength="1"></path>
                                        <defs>
                                            <linearGradient id="paint0_linear_47_27" x1="99.8578" y1="2.70565" x2="99.8578" y2="56.0002" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="{{ \App\Models\SiteSetting::get('primary_color', '#405FF2') }}"></stop>
                                                <stop offset="1" stop-color="{{ \App\Models\SiteSetting::get('secondary_color', '#3FDACF') }}"></stop>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                {{ \App\Models\SiteSetting::get('hero_highlight_text', 'CPD Programs') }}
                            </span>
                        </h3>
                        <p>{{ \App\Models\SiteSetting::get('hero_description', 'Transform your career with industry-recognized CPD courses.') }}</p>
                        <div class="banner__features mt-4">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="feature-item d-flex align-items-center mb-3">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>{{ \App\Models\SiteSetting::get('hero_feature1', 'Accredited Certificates') }}</span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="feature-item d-flex align-items-center mb-3">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span>{{ \App\Models\SiteSetting::get('hero_feature2', 'Flexible Learning') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="banner__btn-wrap">
                            <div class="banner__btn">
                                <a href="{{ route('visitors.courses') }}" class="btn btn-two arrow-btn">{{ \App\Models\SiteSetting::get('hero_button_text', 'Explore Courses') }} <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-7 col-lg-6">
                    <div class="banner__images">
                        <img src="{{ \App\Models\SiteSetting::get('hero_image', 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp') }}" alt="img" class="main-img">
                        <div class="shape big-shape" data-aos="fade-up-right" data-aos-delay="600">
                            <img src="frontend/img/banner/banner_shape01.png" alt="shape" class="tg-motion-effects1">
                        </div>
                        <img src="frontend/img/banner/bg_dots.svg" alt="shape" class="shape bg-dots rotateme">
                        <img src="frontend/img/banner/banner_shape02.png" alt="shape" class="shape small-shape tg-motion-effects3">
                        <div class="banner__author">
                            <div class="banner__author-item">
                                <div class="image">
                                    <img src="{{ \App\Models\SiteSetting::get('hero_author1_image', 'frontend/img/banner/banner_author01.png') }}" alt="img">
                                </div>
                                <h6 class="name">{{ \App\Models\SiteSetting::get('hero_author1_name', 'Robert Fox') }}</h6>
                                <span class="designation">{{ \App\Models\SiteSetting::get('hero_author1_title', 'Diagnostic Expert') }}</span>
                            </div>
                            <div class="banner__author-item">
                                <div class="image">
                                    <img src="{{ \App\Models\SiteSetting::get('hero_author2_image', 'frontend/img/banner/banner_author02.png') }}" alt="img">
                                </div>
                                <h6 class="name">{{ \App\Models\SiteSetting::get('hero_author2_name', 'Michel Jones') }}</h6>
                                <span class="designation">{{ \App\Models\SiteSetting::get('hero_author2_title', 'Medical Specialist') }}</span>
                            </div>
                            <img src="frontend/img/banner/banner_shape02.png" alt="shape" class="shape banner-shape">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <img src="frontend/img/banner/banner_shape01.svg" alt="shape" class="line-shape" data-aos="fade-right" data-aos-delay="1600">
    </section>
    <!-- banner-area-end -->

    <!-- about-area -->
    <section class="about-area tg-motion-effects section-py-120">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-6 col-md-9">
                    <div class="about__images">
                        <img src="{{ \App\Models\SiteSetting::get('about_image', 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp') }}" alt="img" class="main-img">
                        <img src="frontend/img/others/about_shape.svg" alt="img" class="shape alltuchtopdown">
                        <a href="https://www.youtube.com/watch?v=b2Az7_lLh3g" class="popup-video">
                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="28" viewBox="0 0 22 28" fill="none">
                                <path d="M0.19043 26.3132V1.69421C0.190288 1.40603 0.245303 1.12259 0.350273 0.870694C0.455242 0.6188 0.606687 0.406797 0.79027 0.254768C0.973854 0.10274 1.1835 0.0157243 1.39936 0.00193865C1.61521 -0.011847 1.83014 0.0480663 2.02378 0.176003L20.4856 12.3292C20.6973 12.4694 20.8754 12.6856 20.9999 12.9535C21.1245 13.2214 21.1904 13.5304 21.1904 13.8456C21.1904 14.1608 21.1245 14.4697 20.9999 14.7376C20.8754 15.0055 20.6973 15.2217 20.4856 15.3619L2.02378 27.824C1.83056 27.9517 1.61615 28.0116 1.40076 27.9981C1.18536 27.9847 0.97607 27.8983 0.79269 27.7472C0.609313 27.596 0.457816 27.385 0.352299 27.1342C0.246782 26.8833 0.191236 26.6008 0.19043 26.3132Z" fill="currentcolor"></path>
                            </svg>
                        </a>
                        <div class="about__enrolled" data-aos="fade-right" data-aos-delay="200">
                            <p class="title"><span>{{ \App\Models\SiteSetting::get('about_students_count', '36K+') }}</span> {{ \App\Models\SiteSetting::get('about_students_text', 'Enrolled Students') }}</p>
                            <img src="frontend/img/others/student_grp.png" alt="img">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about__content">
                        <div class="section__title">
                            <span class="sub-title">{{ \App\Models\SiteSetting::get('about_subtitle', 'Get To Know About Us') }}</span>
                            <h2 class="title">
                                {{ \App\Models\SiteSetting::get('about_title', 'Empowering Minds,') }}
                                <span class="position-relative">
                                    <svg x="0px" y="0px" preserveAspectRatio="none" viewBox="0 0 209 59" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="path" d="M4.74438 7.70565C69.7006 -1.18799 136.097 -2.38304 203.934 4.1205C207.178 4.48495 209.422 7.14626 208.933 10.0534C206.793 23.6481 205.415 36.5704 204.801 48.8204C204.756 51.3291 202.246 53.5582 199.213 53.7955C136.093 59.7623 74.1922 60.5985 13.5091 56.3043C10.5653 56.0924 7.84371 53.7277 7.42158 51.0325C5.20725 38.2627 2.76333 25.6511 0.0898448 13.1978C-0.465589 10.5873 1.61173 8.1379 4.74438 7.70565Z" stroke="url(#paint0_linear_47_27)" stroke-width="7" pathLength="1"></path>
                                        <defs>
                                            <linearGradient id="paint0_linear_47_27" x1="99.8578" y1="2.70565" x2="99.8578" y2="56.0002" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="{{ \App\Models\SiteSetting::get('primary_color', '#405FF2') }}"></stop>
                                                <stop offset="1" stop-color="{{ \App\Models\SiteSetting::get('secondary_color', '#3FDACF') }}"></stop>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    {{ \App\Models\SiteSetting::get('about_highlight_text', 'Shaping') }}
                                </span>
                                {{ \App\Models\SiteSetting::get('about_title_end', 'Futures') }}
                            </h2>
                        </div>
                        <p class="desc">{{ \App\Models\SiteSetting::get('about_description', 'We are committed to providing high-quality professional development.') }}</p>
                        <ul class="about__info-list list-wrap">
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">{{ \App\Models\SiteSetting::get('about_feature1', 'Industry-Leading Expert Instructors') }}</p>
                            </li>
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">{{ \App\Models\SiteSetting::get('about_feature2', 'Learn Anywhere, Anytime') }}</p>
                            </li>
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">{{ \App\Models\SiteSetting::get('about_feature3', 'Personalized Learning Paths') }}</p>
                            </li>
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">{{ \App\Models\SiteSetting::get('about_feature4', 'Accredited CPD Certificates') }}</p>
                            </li>
                        </ul>
                        <div class="tg-button-wrap">
                            <a href="{{ route('visitors.about') }}" class="btn arrow-btn">{{ \App\Models\SiteSetting::get('about_button_text', 'Start Free Trial') }} <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- about-area-end -->

    @if($featuredCourses->count() > 0)
    <!-- courses-area -->
    <section class="courses-area section-pt-120 section-pb-90" data-background="frontend/img/bg/courses_bg.jpg">
        <div class="container">
            <div class="section__title-wrap">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="section__title text-center mb-40">
                            <span class="sub-title">Featured Courses</span>
                            <h2 class="title">Explore Our World's Best Courses</h2>
                            <p>Discover top-rated courses designed by industry experts to help you master new skills and advance your career.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row courses__grid-wrap row-cols-1 row-cols-xl-4 row-cols-lg-3 row-cols-md-2 row-cols-sm-1">
                @foreach($featuredCourses as $course)
                    <div class="col">
                        <x-course-card :course="$course" />
                    </div>
                @endforeach
            </div>
            <div class="load__more-btn text-center mt-30">
                <a href="{{ route('visitors.courses') }}" class="btn arrow-btn">View All Courses <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
            </div>
        </div>
    </section>
    <!-- courses-area-end -->
    @endif

    <!-- cta-area -->
    <section class="cta-area section-py-120" style="background: linear-gradient(135deg, {{ \App\Models\SiteSetting::get('primary_color', '#5751e1') }} 0%, {{ \App\Models\SiteSetting::get('secondary_color', '#3fdacf') }} 100%);">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="cta-content text-white">
                        <h2 class="cta-title mb-4">{{ \App\Models\SiteSetting::get('cta_title', 'Ready to Start Your Learning Journey?') }}</h2>
                        <p class="cta-desc mb-4">{{ \App\Models\SiteSetting::get('cta_description', 'Join thousands of professionals who have advanced their careers with our CPD programs.') }}</p>
                        <div class="cta-buttons">
                            <a href="{{ route('visitors.courses') }}" class="btn btn-light btn-lg me-3">{{ \App\Models\SiteSetting::get('cta_button1_text', 'Browse Courses') }}</a>
                            <a href="{{ route('visitors.about') }}" class="btn btn-outline-light btn-lg">{{ \App\Models\SiteSetting::get('cta_button2_text', 'Learn More') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- cta-area-end -->

    <!-- fact-area -->
    <section class="fact__area">
        <div class="container">
            <div class="fact__inner-wrap">
                <div class="row">
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">{{ \App\Models\SiteSetting::get('stats_students', '25K') }}</h2>
                            <p>{{ \App\Models\SiteSetting::get('stats_students_text', 'Active Students') }}</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">{{ $courses->count() }}+</h2>
                            <p>{{ \App\Models\SiteSetting::get('stats_courses_text', 'Total Courses') }}</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">{{ \App\Models\SiteSetting::get('stats_cpd_programs', '10') }}</h2>
                            <p>{{ \App\Models\SiteSetting::get('stats_cpd_text', 'CPD Programs') }}</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">{{ \App\Models\SiteSetting::get('stats_satisfaction', '99%') }}</h2>
                            <p>{{ \App\Models\SiteSetting::get('stats_satisfaction_text', 'Satisfaction Rate') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- fact-area-end -->

    <!-- Custom Sections -->
    @php
        $customSections = \App\Models\SiteSetting::where('group', 'custom_sections')->orderBy('sort_order')->get();
    @endphp
    @foreach($customSections as $section)
        @php
            $sectionData = is_array($section->value) ? $section->value : json_decode($section->value, true);
        @endphp
        @if($sectionData && isset($sectionData['enabled']) && $sectionData['enabled'] && !empty($sectionData['content']))
            <section class="custom-section section-py-120">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="custom-section-content text-center">
                                <h2 class="mb-4">{{ $sectionData['name'] ?? 'Custom Section' }}</h2>
                                @if($sectionData['type'] === 'text')
                                    <div class="custom-text-content">
                                        {!! nl2br(e($sectionData['content'])) !!}
                                    </div>
                                @elseif($sectionData['type'] === 'video' && !empty($sectionData['content']))
                                    <div class="custom-video-content">
                                        <div class="ratio ratio-16x9">
                                            <iframe src="{{ $sectionData['content'] }}" allowfullscreen></iframe>
                                        </div>
                                    </div>
                                @elseif($sectionData['type'] === 'testimonial')
                                    <div class="custom-testimonial-content">
                                        <blockquote class="blockquote">
                                            <p class="mb-4">{{ $sectionData['content'] }}</p>
                                        </blockquote>
                                    </div>
                                @else
                                    <div class="custom-content">
                                        {!! nl2br(e($sectionData['content'])) !!}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif
    @endforeach
    <!-- Custom Sections End -->
</main>

<style>
:root {
    --tg-theme-primary: {{ \App\Models\SiteSetting::get('primary_color', '#5751e1') }};
    --tg-theme-secondary: {{ \App\Models\SiteSetting::get('secondary_color', '#ffc224') }};
    --tg-common-color-blue: {{ \App\Models\SiteSetting::get('primary_color', '#050071') }};
    --tg-common-color-blue-2: #282568;
    --tg-common-color-dark: #1c1a4a;
    --tg-common-color-black: #06042e;
    --tg-common-color-dark-2: #4a44d1;
}
</style>
@endsection