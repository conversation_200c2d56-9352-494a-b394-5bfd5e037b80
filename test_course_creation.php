<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Http\Controllers\Admin\CourseController;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use App\Models\ZoomMeeting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

echo "=== Course Creation with Live Session Test ===\n\n";

// Log the start
Log::info('Starting course creation test with live session');

// 1. Check prerequisites
echo "1. Checking prerequisites:\n";
$category = Category::first();
$user = User::first();

if (!$category) {
    echo "✗ No categories found. Creating one...\n";
    $category = Category::create([
        'name' => 'Test Category',
        'slug' => 'test-category',
        'description' => 'Test category for debugging',
        'is_active' => true,
    ]);
    echo "✓ Category created: {$category->name}\n";
} else {
    echo "✓ Category found: {$category->name}\n";
}

if (!$user) {
    echo "✗ No users found\n";
    exit(1);
} else {
    echo "✓ User found: {$user->name}\n";
}

// 2. Simulate the request data
echo "\n2. Preparing request data:\n";
$futureDate = date('Y-m-d', strtotime('+7 days'));
$futureTime = '14:30';

$requestData = [
    'title' => 'Test Course with Live Session - ' . date('Y-m-d H:i:s'),
    'short_description' => 'Test course description',
    'description' => 'Detailed test course description',
    'category_id' => $category->id,
    'instructor_id' => $user->id,
    'status' => 'draft',
    'duration' => '2.5',
    'difficulty_level' => 'beginner',
    'language' => 'English',
    'is_free' => '1',
    'has_live_session' => '1',
    'meeting_topic' => 'Test Live Session',
    'meeting_description' => 'Test meeting description',
    'meeting_start_date' => $futureDate,
    'meeting_start_time' => $futureTime,
    'meeting_duration' => '60',
    'minimum_attendance_minutes' => '45',
];

echo "Meeting date: {$futureDate}\n";
echo "Meeting time: {$futureTime}\n";
echo "Combined datetime: {$futureDate} {$futureTime}\n";

// 3. Test the course creation logic manually
echo "\n3. Testing course creation logic:\n";

try {
    // Set authenticated user
    Auth::login($user);
    
    // Create the course first
    $courseData = array_merge($requestData, [
        'created_by' => $user->id,
        'updated_by' => $user->id,
    ]);
    
    // Remove meeting-specific fields for course creation
    unset($courseData['meeting_topic'], $courseData['meeting_description'], 
          $courseData['meeting_start_date'], $courseData['meeting_start_time'], 
          $courseData['meeting_duration'], $courseData['minimum_attendance_minutes']);
    
    $course = Course::create($courseData);
    echo "✓ Course created: {$course->title} (ID: {$course->id})\n";
    
    // Now test the Zoom meeting creation
    echo "\n4. Testing Zoom meeting creation:\n";
    
    // Create a mock request object
    $request = new Request($requestData);
    
    // Test the combineDateTimeFields method
    $controller = new CourseController(new \App\Services\ZoomService());
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('combineDateTimeFields');
    $method->setAccessible(true);
    
    $combinedDateTime = $method->invoke($controller, $request);
    echo "Combined datetime result: {$combinedDateTime}\n";
    
    if ($combinedDateTime) {
        $request->merge(['meeting_start_time' => $combinedDateTime]);
        
        // Test the createZoomMeeting method
        $createMethod = $reflection->getMethod('createZoomMeeting');
        $createMethod->setAccessible(true);
        
        echo "Attempting to create Zoom meeting...\n";
        $createMethod->invoke($controller, $course, $request);
        
        echo "✓ Zoom meeting creation completed\n";
    } else {
        echo "✗ Failed to combine date and time fields\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// 5. Check results
echo "\n5. Checking results:\n";
$totalCourses = Course::count();
$totalMeetings = ZoomMeeting::count();
$coursesWithLiveSessions = Course::where('has_live_session', true)->count();

echo "Total courses: {$totalCourses}\n";
echo "Courses with live sessions: {$coursesWithLiveSessions}\n";
echo "Total Zoom meetings: {$totalMeetings}\n";

if ($totalMeetings > 0) {
    $latestMeeting = ZoomMeeting::latest()->first();
    echo "Latest meeting: {$latestMeeting->topic} (Course ID: {$latestMeeting->course_id})\n";
}

echo "\n=== Test Complete ===\n";
