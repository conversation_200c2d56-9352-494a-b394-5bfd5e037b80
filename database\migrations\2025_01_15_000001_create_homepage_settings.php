<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Homepage settings will use the existing site_settings table
        // This migration just ensures the table exists with proper indexes
        if (!Schema::hasTable('site_settings')) {
            Schema::create('site_settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->longText('value')->nullable();
                $table->string('type')->default('text');
                $table->string('group')->default('general');
                $table->text('description')->nullable();
                $table->integer('sort_order')->default(0);
                $table->timestamps();
                
                $table->index(['group', 'sort_order']);
                $table->index('key');
            });
        } else {
            // Add indexes if they don't exist
            Schema::table('site_settings', function (Blueprint $table) {
                if (!Schema::hasIndex('site_settings', 'site_settings_group_sort_order_index')) {
                    $table->index(['group', 'sort_order']);
                }
                if (!Schema::hasIndex('site_settings', 'site_settings_key_index')) {
                    $table->index('key');
                }
            });
        }
    }

    public function down()
    {
        // Don't drop the table as it might be used by other parts of the system
    }
};