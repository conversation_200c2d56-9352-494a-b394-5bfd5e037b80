<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('assessments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->enum('type', ['quiz', 'exam', 'assignment', 'project'])->default('quiz');
            $table->decimal('score', 8, 2)->default(0);
            $table->decimal('max_score', 8, 2);
            $table->boolean('passed')->default(false);
            $table->timestamp('completed_at')->useCurrent();
            $table->integer('attempts')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('assessments');
    }
};