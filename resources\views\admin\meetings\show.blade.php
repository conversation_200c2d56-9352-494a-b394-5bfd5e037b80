@extends('admin.dashboard')

@section('styles')
<!-- DataTables -->
<link href="{{ asset('assets/libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
<link href="{{ asset('assets/libs/datatables.net-buttons-bs4/css/buttons.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
<link href="{{ asset('assets/libs/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Meeting Details</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.meetings.index') }}">Meetings</a></li>
                                <li class="breadcrumb-item active">{{ $meeting->topic }}</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="row">
                <!-- Meeting Information -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-4">
                                <div>
                                    <h4 class="card-title">{{ $meeting->topic }}</h4>
                                    <p class="card-title-desc">{{ $meeting->description }}</p>
                                </div>
                                <div class="d-flex gap-2">
                                    @if($meeting->join_url)
                                        <a href="{{ $meeting->join_url }}" target="_blank" class="btn btn-success btn-sm">
                                            <i class="uil-external-link-alt me-1"></i> Join Meeting
                                        </a>
                                    @endif
                                    @if($meeting->start_url)
                                        <a href="{{ $meeting->start_url }}" target="_blank" class="btn btn-primary btn-sm">
                                            <i class="uil-play me-1"></i> Start Meeting
                                        </a>
                                    @endif
                                </div>
                            </div>

                            <!-- Meeting Details -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-borderless mb-0">
                                            <tbody>
                                                <tr>
                                                    <td class="fw-bold">Meeting ID:</td>
                                                    <td>{{ $meeting->zoom_meeting_id }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Status:</td>
                                                    <td>
                                                        @switch($meeting->status)
                                                            @case('scheduled')
                                                                <span class="badge bg-primary">
                                                                    <i class="uil-calendar-alt me-1"></i>Scheduled
                                                                </span>
                                                                @break
                                                            @case('started')
                                                                <span class="badge bg-success">
                                                                    <i class="uil-play-circle me-1"></i>Live
                                                                </span>
                                                                @break
                                                            @case('ended')
                                                                <span class="badge bg-secondary">
                                                                    <i class="uil-stop-circle me-1"></i>Ended
                                                                </span>
                                                                @break
                                                            @case('cancelled')
                                                                <span class="badge bg-danger">
                                                                    <i class="uil-times-circle me-1"></i>Cancelled
                                                                </span>
                                                                @break
                                                        @endswitch
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Date & Time:</td>
                                                    <td>{{ $meeting->formatted_start_time }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Duration:</td>
                                                    <td>{{ $meeting->formatted_duration }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Timezone:</td>
                                                    <td>{{ $meeting->timezone }}</td>
                                                </tr>
                                                @if($meeting->password)
                                                <tr>
                                                    <td class="fw-bold">Password:</td>
                                                    <td>
                                                        <span class="badge bg-warning">
                                                            <i class="uil-lock me-1"></i>Protected
                                                        </span>
                                                    </td>
                                                </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="table-responsive">
                                        <table class="table table-borderless mb-0">
                                            <tbody>
                                                <tr>
                                                    <td class="fw-bold">Min. Attendance:</td>
                                                    <td>{{ $meeting->minimum_attendance_minutes }} minutes</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Required for Completion:</td>
                                                    <td>
                                                        @if($meeting->attendance_required_for_completion)
                                                            <span class="badge bg-info">
                                                                <i class="uil-check me-1"></i>Yes
                                                            </span>
                                                        @else
                                                            <span class="badge bg-light text-dark">
                                                                <i class="uil-times me-1"></i>No
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Recording:</td>
                                                    <td>
                                                        @if($meeting->recording_available)
                                                            <span class="badge bg-success">
                                                                <i class="uil-record-audio me-1"></i>Available
                                                            </span>
                                                            @if($meeting->recording_url)
                                                                <br><a href="{{ $meeting->recording_url }}" target="_blank" class="small">View Recording</a>
                                                            @endif
                                                        @else
                                                            <span class="badge bg-light text-muted">
                                                                <i class="uil-times me-1"></i>Not Available
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Created By:</td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ $meeting->creator->avatar ?? asset('assets/images/users/avatar-default.jpg') }}"
                                                                 alt="{{ $meeting->creator->name }}"
                                                                 class="rounded-circle me-2" style="width: 24px; height: 24px;">
                                                            {{ $meeting->creator->name }}
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Created:</td>
                                                    <td>{{ $meeting->created_at->format('M d, Y g:i A') }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Information -->
                            <div class="border-top pt-4">
                                <h5 class="mb-3">Associated Course</h5>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        @if($meeting->course->thumbnail)
                                            <img src="{{ asset('storage/' . $meeting->course->thumbnail) }}" alt="{{ $meeting->course->title }}"
                                                 class="rounded" style="width: 80px; height: 60px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                 style="width: 80px; height: 60px;">
                                                <i class="uil-book-open text-muted"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $meeting->course->title }}</h6>
                                        <p class="text-muted mb-1">{{ $meeting->course->short_description }}</p>
                                        <div>
                                            <span class="badge bg-primary">{{ $meeting->course->category->name ?? 'Uncategorized' }}</span>
                                            @if($meeting->course->is_featured)
                                                <span class="badge bg-warning"><i class="uil-star"></i> Featured</span>
                                            @endif
                                            @if($meeting->course->is_free)
                                                <span class="badge bg-success">Free</span>
                                            @else
                                                <span class="badge bg-info">KES {{ number_format($meeting->course->price, 0) }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div>
                                        <a href="{{ route('admin.courses.edit', $meeting->course->id) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="uil-edit me-1"></i> Edit Course
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics & Actions -->
                <div class="col-lg-4">
                    <!-- Statistics -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Attendance Statistics</h5>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="p-3">
                                        <h4 class="text-primary mb-1">{{ $stats['total_enrolled'] }}</h4>
                                        <p class="text-muted mb-0 small">Total Enrolled</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3">
                                        <h4 class="text-success mb-1">{{ $stats['total_attendees'] }}</h4>
                                        <p class="text-muted mb-0 small">Attended</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3">
                                        <h4 class="text-info mb-1">{{ $stats['qualified_attendees'] }}</h4>
                                        <p class="text-muted mb-0 small">Qualified</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3">
                                        <h4 class="text-warning mb-1">{{ $stats['attendance_rate'] }}%</h4>
                                        <p class="text-muted mb-0 small">Attendance Rate</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small">Attendance Progress</span>
                                    <span class="small">{{ $stats['attendance_rate'] }}%</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ $stats['attendance_rate'] }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Quick Actions</h5>
                            
                            <div class="d-grid gap-2">
                                <form action="{{ route('admin.meetings.sync-attendance', $meeting->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="uil-refresh me-2"></i>Sync Attendance
                                    </button>
                                </form>
                                
                                <form action="{{ route('admin.meetings.update-status', $meeting->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-info w-100">
                                        <i class="uil-sync me-2"></i>Update Status
                                    </button>
                                </form>

                                @if(!$meeting->recording_available)
                                <button type="button" class="btn btn-outline-warning w-100" data-bs-toggle="modal" data-bs-target="#recordingModal">
                                    <i class="uil-record-audio me-2"></i>Add Recording URL
                                </button>
                                @endif

                                <a href="{{ route('admin.courses.edit', $meeting->course->id) }}" class="btn btn-outline-secondary w-100">
                                    <i class="uil-edit me-2"></i>Edit Course
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Meeting Links -->
                    @if($meeting->join_url || $meeting->start_url)
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Meeting Links</h5>
                            
                            @if($meeting->join_url)
                            <div class="mb-3">
                                <label class="form-label small fw-bold">Join URL:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" value="{{ $meeting->join_url }}" readonly>
                                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyToClipboard('{{ $meeting->join_url }}')">
                                        <i class="uil-copy"></i>
                                    </button>
                                </div>
                            </div>
                            @endif

                            @if($meeting->start_url)
                            <div class="mb-3">
                                <label class="form-label small fw-bold">Start URL:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" value="{{ $meeting->start_url }}" readonly>
                                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyToClipboard('{{ $meeting->start_url }}')">
                                        <i class="uil-copy"></i>
                                    </button>
                                </div>
                            </div>
                            @endif

                            @if($meeting->password)
                            <div class="mb-0">
                                <label class="form-label small fw-bold">Password:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" value="{{ $meeting->password }}" readonly>
                                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyToClipboard('{{ $meeting->password }}')">
                                        <i class="uil-copy"></i>
                                    </button>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title">Attendance Records</h4>
                                    <p class="card-title-desc">Track participant attendance and engagement for this meeting.</p>
                                </div>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="exportAttendance()">
                                        <i class="uil-export me-1"></i> Export CSV
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="refreshAttendance()">
                                        <i class="uil-refresh me-1"></i> Refresh
                                    </button>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="search-box">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="searchAttendance" placeholder="Search participants...">
                                            <i class="uil-search search-icon"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="statusFilter">
                                        <option value="">All Status</option>
                                        <option value="joined">Joined</option>
                                        <option value="left">Left</option>
                                        <option value="in_meeting">In Meeting</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="qualifiedFilter">
                                        <option value="">All Participants</option>
                                        <option value="qualified">Qualified</option>
                                        <option value="not_qualified">Not Qualified</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="userTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="registered">Registered Users</option>
                                        <option value="guest">Guests</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                        <i class="uil-times me-1"></i> Clear
                                    </button>
                                </div>
                            </div>

                            <!-- Attendance Stats -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card border-0 bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-users-alt display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meeting->attendances->count() }}</h5>
                                                    <p class="mb-0">Total Participants</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-check-circle display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meeting->attendances->where('attended_full_session', true)->count() }}</h5>
                                                    <p class="mb-0">Qualified</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-user-check display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meeting->attendances->whereNotNull('user_id')->count() }}</h5>
                                                    <p class="mb-0">Registered Users</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-clock display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ round($meeting->attendances->avg('duration_minutes') ?? 0) }}m</h5>
                                                    <p class="mb-0">Avg Duration</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover" id="attendanceTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 50px;">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                                </div>
                                            </th>
                                            <th>Participant</th>
                                            <th>Email</th>
                                            <th>Join Time</th>
                                            <th>Leave Time</th>
                                            <th>Duration</th>
                                            <th>Status</th>
                                            <th>Qualified</th>
                                            <th style="width: 120px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($meeting->attendances as $attendance)
                                        <tr>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input attendance-checkbox" type="checkbox" value="{{ $attendance->id }}">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <img src="{{ $attendance->user->avatar ?? asset('assets/images/users/avatar-default.jpg') }}"
                                                             alt="{{ $attendance->name }}"
                                                             class="rounded-circle" style="width: 40px; height: 40px;">
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">{{ $attendance->name }}</h6>
                                                        <p class="text-muted mb-0 small">
                                                            @if($attendance->user)
                                                                <i class="uil-user text-success me-1"></i>Registered User
                                                            @else
                                                                <i class="uil-user-times text-muted me-1"></i>Guest
                                                            @endif
                                                        </p>
                                                        @if($attendance->participant_uuid)
                                                            <div class="mt-1">
                                                                <span class="badge bg-light text-dark small">UUID: {{ Str::limit($attendance->participant_uuid, 8) }}</span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($attendance->email)
                                                    <span class="text-primary">{{ $attendance->email }}</span>
                                                @else
                                                    <span class="text-muted">Not provided</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($attendance->joined_at)
                                                    <div>
                                                        <h6 class="mb-0 small">{{ $attendance->joined_at->format('M d, Y') }}</h6>
                                                        <p class="text-muted mb-0 small">{{ $attendance->joined_at->format('g:i A') }}</p>
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($attendance->left_at)
                                                    <div>
                                                        <h6 class="mb-0 small">{{ $attendance->left_at->format('M d, Y') }}</h6>
                                                        <p class="text-muted mb-0 small">{{ $attendance->left_at->format('g:i A') }}</p>
                                                    </div>
                                                @else
                                                    @if($attendance->status === 'in_meeting')
                                                        <span class="badge bg-success">Still in meeting</span>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                @endif
                                            </td>
                                            <td>
                                                @if($attendance->duration_minutes > 0)
                                                    <div>
                                                        <span class="badge bg-info">{{ $attendance->duration_minutes }} min</span>
                                                        @if($attendance->duration_minutes >= $meeting->minimum_attendance_minutes)
                                                            <br><small class="text-success">✓ Met requirement</small>
                                                        @else
                                                            <br><small class="text-warning">⚠ Below minimum</small>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @switch($attendance->status)
                                                    @case('joined')
                                                        <span class="badge bg-primary">
                                                            <i class="uil-sign-in-alt me-1"></i>Joined
                                                        </span>
                                                        @break
                                                    @case('left')
                                                        <span class="badge bg-secondary">
                                                            <i class="uil-sign-out-alt me-1"></i>Left
                                                        </span>
                                                        @break
                                                    @case('in_meeting')
                                                        <span class="badge bg-success">
                                                            <i class="uil-video me-1"></i>In Meeting
                                                        </span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-light text-dark">{{ ucfirst($attendance->status) }}</span>
                                                @endswitch
                                            </td>
                                            <td>
                                                @if($attendance->attended_full_session)
                                                    <span class="badge bg-success">
                                                        <i class="uil-check me-1"></i>Qualified
                                                    </span>
                                                @else
                                                    <span class="badge bg-warning">
                                                        <i class="uil-times me-1"></i>Not Qualified
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-light btn-sm dropdown-toggle" type="button"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        @if($attendance->user)
                                                        <li>
                                                            <a class="dropdown-item" href="#">
                                                                <i class="uil-user me-2"></i>View Profile
                                                            </a>
                                                        </li>
                                                        @endif
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="viewAttendanceDetails({{ $attendance->id }})">
                                                                <i class="uil-eye me-2"></i>View Details
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" onclick="removeAttendance({{ $attendance->id }})">
                                                                <i class="uil-trash me-2"></i>Remove Record
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="uil-users-alt display-4 text-muted mb-3"></i>
                                                    <h5 class="text-muted">No attendance records found</h5>
                                                    <p class="text-muted">Attendance data will appear here once participants join the meeting</p>
                                                    <button class="btn btn-primary" onclick="refreshAttendance()">
                                                        <i class="uil-refresh me-1"></i> Sync from Zoom
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            {{-- Bulk Actions --}}
                            @if($meeting->attendances->count() > 0)
                            <div class="row mt-3" id="bulkActions" style="display: none;">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span><span id="selectedCount">0</span> participants selected</span>
                                            <div>
                                                <button class="btn btn-sm btn-success me-2" onclick="bulkAction('qualify')">
                                                    <i class="uil-check me-1"></i>Mark Qualified
                                                </button>
                                                <button class="btn btn-sm btn-warning me-2" onclick="bulkAction('disqualify')">
                                                    <i class="uil-times me-1"></i>Mark Not Qualified
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="bulkAction('remove')">
                                                    <i class="uil-trash me-1"></i>Remove Records
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

<!-- Recording URL Modal -->
<div class="modal fade" id="recordingModal" tabindex="-1" aria-labelledby="recordingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordingModalLabel">Add Recording URL</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.meetings.update-recording', $meeting->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="recording_url" class="form-label">Recording URL</label>
                        <input type="url" class="form-control" id="recording_url" name="recording_url" 
                               placeholder="https://zoom.us/rec/..." required>
                        <div class="form-text">Enter the Zoom recording URL for this meeting</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Recording URL</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="uil-check me-2"></i>Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toast);
            });
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            alert('Failed to copy to clipboard');
        });
    }

    function exportAttendance() {
        // Create CSV content
        const attendanceData = @json($meeting->attendances);
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Name,Email,Joined At,Left At,Duration (minutes),Status,Qualified\n";
        
        attendanceData.forEach(function(attendance) {
            const row = [
                attendance.name || '',
                attendance.email || '',
                attendance.joined_at || '',
                attendance.left_at || '',
                attendance.duration_minutes || 0,
                attendance.status || '',
                attendance.attended_full_session ? 'Yes' : 'No'
            ].map(field => `"${field}"`).join(',');
            csvContent += row + "\n";
        });

        // Create download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "meeting_attendance_{{ $meeting->zoom_meeting_id }}.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
</script>
@endsection
