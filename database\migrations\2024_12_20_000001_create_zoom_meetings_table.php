<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zoom_meetings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('courses')->onDelete('cascade');
            $table->string('zoom_meeting_id')->unique(); // Zoom's meeting ID
            $table->string('topic');
            $table->text('description')->nullable();
            $table->dateTime('start_time');
            $table->integer('duration'); // Duration in minutes
            $table->string('timezone')->default('Africa/Nairobi');
            $table->string('join_url');
            $table->string('start_url');
            $table->string('password')->nullable();
            $table->enum('status', ['scheduled', 'started', 'ended', 'cancelled'])->default('scheduled');
            $table->string('recording_url')->nullable();
            $table->boolean('recording_available')->default(false);
            $table->integer('minimum_attendance_minutes')->default(45); // Required attendance time
            $table->boolean('attendance_required_for_completion')->default(true);
            $table->json('zoom_response')->nullable(); // Store full Zoom API response
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes
            $table->index(['course_id', 'status']);
            $table->index('start_time');
            $table->index('zoom_meeting_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zoom_meetings');
    }
};
