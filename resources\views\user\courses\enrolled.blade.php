@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Enrolled Courses</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Enrolled Courses</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle mb-0">
                                    <thead>
                                        <tr>
                                            <th>Course</th>
                                            <th>Instructor</th>
                                            <th>Progress</th>
                                            <th>Enrolled Date</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($enrollments as $enrollment)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        @if($enrollment->course->thumbnail)
                                                            <img src="{{ asset('storage/' . $enrollment->course->thumbnail) }}" alt="" class="img-fluid rounded">
                                                        @else
                                                            <div class="avatar-title rounded bg-primary">
                                                                {{ substr($enrollment->course->title, 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <h5 class="font-size-14 mb-1">{{ $enrollment->course->title }}</h5>
                                                        <p class="text-muted mb-0">{{ $enrollment->course->category->name ?? 'General' }}</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $enrollment->course->instructor->name ?? 'N/A' }}</td>
                                            <td>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-primary" role="progressbar" 
                                                         style="width: {{ $enrollment->progress_percentage }}%" 
                                                         aria-valuenow="{{ $enrollment->progress_percentage }}" 
                                                         aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <span class="text-muted font-size-12">{{ $enrollment->progress_percentage }}%</span>
                                            </td>
                                            <td>{{ $enrollment->enrolled_at->format('M d, Y') }}</td>
                                            <td>
                                                <a href="{{ route('course.show', $enrollment->course->slug) }}" class="btn btn-primary btn-sm">Continue</a>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <p class="text-muted mb-0">No enrolled courses found.</p>
                                                <a href="{{ route('visitors.courses') }}" class="btn btn-primary btn-sm mt-2">Browse Courses</a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            
                            @if($enrollments->hasPages())
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="pagination-wrap hstack gap-2 justify-content-center">
                                        {{ $enrollments->links() }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection