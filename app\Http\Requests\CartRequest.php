<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Course;

class CartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'quantity' => 'sometimes|integer|min:1|max:10',
        ];

        // Add course-specific validation for add operations
        if ($this->isMethod('post') && $this->route('courseId')) {
            $rules['course_id'] = [
                'required',
                'integer',
                'exists:courses,id',
                function ($attribute, $value, $fail) {
                    $course = Course::find($value);
                    
                    if (!$course) {
                        $fail('The selected course does not exist.');
                        return;
                    }

                    if ($course->status !== 'published') {
                        $fail('This course is not available for purchase.');
                        return;
                    }

                    // Additional business logic checks can be added here
                    // For example: enrollment limits, prerequisites, etc.
                },
            ];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'quantity.integer' => 'Quantity must be a valid number.',
            'quantity.min' => 'Quantity must be at least 1.',
            'quantity.max' => 'You cannot add more than 10 of the same course.',
            'course_id.exists' => 'The selected course does not exist.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Add course ID from route parameter for validation
        if ($this->route('courseId')) {
            $this->merge([
                'course_id' => $this->route('courseId'),
            ]);
        }
    }

    /**
     * Get the validated course instance
     */
    public function getCourse(): ?Course
    {
        if ($this->route('courseId')) {
            return Course::with('instructor', 'category')->find($this->route('courseId'));
        }

        return null;
    }

    /**
     * Check if the course can be added to cart
     */
    public function canAddToCart(): array
    {
        $course = $this->getCourse();
        
        if (!$course) {
            return [
                'can_add' => false,
                'reason' => 'Course not found.',
            ];
        }

        if ($course->status !== 'published') {
            return [
                'can_add' => false,
                'reason' => 'This course is not available for purchase.',
            ];
        }

        // Check if course is already enrolled (if user is authenticated)
        if (auth()->check()) {
            // TODO: Implement enrollment check when enrollment system is ready
            // $isEnrolled = $course->enrollments()->where('user_id', auth()->id())->exists();
            // if ($isEnrolled) {
            //     return [
            //         'can_add' => false,
            //         'reason' => 'You are already enrolled in this course.',
            //     ];
            // }
        }

        return [
            'can_add' => true,
            'reason' => null,
        ];
    }
}
