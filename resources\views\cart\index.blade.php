@extends('welcome')

@section('content')
<main class="main-area fix">
    <!-- breadcrumb-area -->
    <section class="breadcrumb__area breadcrumb__bg" data-background="{{ asset('frontend/img/bg/breadcrumb_bg.jpg') }}">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="breadcrumb__content">
                        <h3 class="title">Shopping Cart</h3>
                        <nav class="breadcrumb">
                            <span property="itemListElement" typeof="ListItem">
                                <a href="{{ url('/') }}">Home</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">Shopping Cart</span>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <!-- cart-area -->
    <section class="cart__area section-py-120">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    @if(isset($validation) && $validation['status'] === 'warning')
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            {{ $validation['message'] }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(empty($cartItems))
                        <div class="cart__empty">
                            <div class="text-center">
                                <img src="{{ asset('frontend/img/icons/empty-cart.svg') }}" alt="Empty Cart" class="mb-4" style="width: 150px;">
                                <h3>Your cart is empty</h3>
                                <p class="mb-4">Looks like you haven't added any courses to your cart yet.</p>
                                <a href="{{ route('visitors.courses') }}" class="btn btn-two">Browse Courses</a>
                            </div>
                        </div>
                    @else
                        <div class="cart__wrapper">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="cart__table-wrap">
                                        <table class="cart__table">
                                            <thead>
                                                <tr>
                                                    <th class="product">Course</th>
                                                    <th class="price">Price</th>
                                                    <th class="quantity">Quantity</th>
                                                    <th class="total">Total</th>
                                                    <th class="remove">Remove</th>
                                                </tr>
                                            </thead>
                                            <tbody id="cart-items">
                                                @foreach($cartItems as $item)
                                                    <tr data-course-id="{{ $item['id'] }}">
                                                        <td class="product">
                                                            <div class="cart__product">
                                                                <div class="cart__product-thumb">
                                                                    @if($item['thumbnail'])
                                                                        <img src="{{ asset('storage/' . $item['thumbnail']) }}" alt="{{ $item['title'] }}">
                                                                    @else
                                                                        <img src="{{ asset('frontend/img/course/course_thumb01.jpg') }}" alt="{{ $item['title'] }}">
                                                                    @endif
                                                                </div>
                                                                <div class="cart__product-content">
                                                                    <h4 class="title">
                                                                        <a href="{{ route('course.show', $item['slug']) }}">{{ $item['title'] }}</a>
                                                                    </h4>
                                                                    <span class="instructor">By {{ $item['instructor'] }}</span>
                                                                    @if($item['duration'])
                                                                        <span class="duration">{{ $item['duration'] }} Hours</span>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="price">
                                                            @if($item['is_free'])
                                                                <span class="amount">Free</span>
                                                            @else
                                                                <span class="amount">KES {{ number_format($item['price'], 2) }}</span>
                                                            @endif
                                                        </td>
                                                        <td class="quantity">
                                                            <div class="cart-plus-minus">
                                                                <input type="text" value="{{ $item['quantity'] }}" readonly>
                                                            </div>
                                                        </td>
                                                        <td class="total">
                                                            @if($item['is_free'])
                                                                <span class="amount">Free</span>
                                                            @else
                                                                <span class="amount item-total">KES {{ number_format($item['price'] * $item['quantity'], 2) }}</span>
                                                            @endif
                                                        </td>
                                                        <td class="remove">
                                                            <a href="javascript:;" class="remove-item" data-course-id="{{ $item['id'] }}" title="Remove from cart">
                                                                <i class="fas fa-times"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="cart__actions">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <a href="{{ route('visitors.courses') }}" class="btn btn-outline">Continue Shopping</a>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="button" class="btn btn-outline clear-cart">Clear Cart</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4">
                                    <div class="cart__sidebar">
                                        <div class="cart__summary">
                                            <h4 class="title">Cart Summary</h4>
                                            <ul class="cart__summary-list">
                                                <li>
                                                    <span>Total Courses:</span>
                                                    <span id="total-courses">{{ $cartSummary['item_count'] }}</span>
                                                </li>
                                                @if($cartSummary['free_courses'] > 0)
                                                    <li>
                                                        <span>Free Courses:</span>
                                                        <span>{{ $cartSummary['free_courses'] }}</span>
                                                    </li>
                                                @endif
                                                @if($cartSummary['paid_courses'] > 0)
                                                    <li>
                                                        <span>Paid Courses:</span>
                                                        <span>{{ $cartSummary['paid_courses'] }}</span>
                                                    </li>
                                                @endif
                                                <li class="subtotal">
                                                    <span>Subtotal:</span>
                                                    <span id="cart-subtotal">{{ $cartSummary['subtotal_formatted'] }}</span>
                                                </li>
                                                <li class="total">
                                                    <span>Total:</span>
                                                    <span id="cart-total">{{ $cartSummary['total_formatted'] }}</span>
                                                </li>
                                            </ul>

                                            @if($cartSummary['total'] > 0)
                                                <div class="cart__checkout">
                                                    <a href="{{ route('checkout.index') }}" class="btn btn-two w-100">
                                                        Proceed to Checkout
                                                    </a>
                                                </div>
                                            @else
                                                <div class="cart__checkout">
                                                    <button type="button" class="btn btn-two w-100" disabled>
                                                        All courses are free - Enroll directly
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
    <!-- cart-area-end -->
</main>

<style>
.cart__empty {
    padding: 80px 0;
}

.cart__table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

.cart__table th,
.cart__table td {
    padding: 20px 15px;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.cart__table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.cart__product {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cart__product-thumb img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.cart__product-content .title {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.cart__product-content .instructor,
.cart__product-content .duration {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 3px;
}

.cart__summary {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
}

.cart__summary-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.cart__summary-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
}

.cart__summary-list li.total {
    font-weight: 600;
    font-size: 18px;
    border-bottom: none;
    padding-top: 15px;
}

.remove-item {
    color: #dc3545;
    font-size: 18px;
    transition: color 0.3s;
}

.remove-item:hover {
    color: #c82333;
}
</style>
@endsection
