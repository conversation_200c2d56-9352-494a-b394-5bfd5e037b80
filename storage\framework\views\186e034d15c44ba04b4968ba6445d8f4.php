<div class="col">
    <div class="courses__item shine__animate-item">
        <div class="courses__item-thumb">
            <a href="<?php echo e(route('course.show', $course->slug)); ?>" class="shine__animate-link">
                <?php if($course->thumbnail): ?>
                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>">
                <?php else: ?>
                    <img src="<?php echo e(asset('frontend/img/course/course_thumb01.jpg')); ?>" alt="<?php echo e($course->title); ?>">
                <?php endif; ?>
            </a>
            <a href="javascript:;" class="wsus-wishlist-btn common-white courses__wishlist-two"
               aria-label="WishList" data-slug="<?php echo e($course->slug); ?>">
                <i class="far fa-heart"></i>
            </a>
        </div>
        <div class="courses__item-content">
            <ul class="courses__item-meta list-wrap">
                <li class="courses__item-tag">
                    <a href="<?php echo e(route('visitors.courses')); ?>?category=<?php echo e($course->category->slug); ?>">
                        <?php echo e($course->category->name); ?>

                    </a>
                </li>
                <li class="avg-rating">
                    <i class="fas fa-star"></i>
                    5.0 
                </li>
            </ul>
            <h3 class="title">
                <a href="<?php echo e(route('course.show', $course->slug)); ?>"><?php echo e($course->title); ?></a>
            </h3>
            <p class="author">
                By <a href="#" class="instructor-link"><?php echo e($course->instructor->name); ?></a>
            </p>
            <div class="courses__item-bottom">
                <div class="button">
                    <?php if($course->is_free): ?>
                        <a href="<?php echo e(route('course.show', $course->slug)); ?>" class="enroll-free">
                            <span class="text">Enroll Free</span>
                            <i class="flaticon-arrow-right"></i>
                        </a>
                    <?php else: ?>
                        <a href="javascript:;" class="add-to-cart" data-id="<?php echo e($course->id); ?>" data-title="<?php echo e($course->title); ?>">
                            <span class="text">Add To Cart</span>
                            <i class="flaticon-arrow-right"></i>
                        </a>
                    <?php endif; ?>
                </div>

                <h4 class="price">
                    <?php if($course->is_free): ?>
                        Free
                    <?php else: ?>
                        <?php if(isset($course->has_discount) ? $course->has_discount : ($course->discount_price && $course->discount_price < $course->price)): ?>
                            <span class="old-price">KSh <?php echo e(number_format($course->price, 0)); ?></span>
                            KSh <?php echo e(number_format($course->discount_price, 0)); ?>

                        <?php else: ?>
                            KSh <?php echo e(number_format($course->price, 0)); ?>

                        <?php endif; ?>
                    <?php endif; ?>
                </h4>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/partials/course-card.blade.php ENDPATH**/ ?>