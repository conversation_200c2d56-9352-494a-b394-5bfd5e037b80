<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Category;
use App\Services\ZoomService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\CourseController;

class CourseControllerDateTimeTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);
        
        // Create a test category
        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);
    }

    public function test_datetime_combination_with_both_fields()
    {
        $this->actingAs($this->user);

        // Use a future date
        $futureDate = now()->addDays(7)->format('Y-m-d');

        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course with Live Session',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Test Meeting',
            'meeting_description' => 'Test meeting description',
            'meeting_start_date' => $futureDate,
            'meeting_start_time' => '14:30',
            'meeting_duration' => '60',
        ]);

        // Should redirect successfully without validation errors
        $response->assertRedirect(route('admin.courses.index'));
        $response->assertSessionHas('success');
        
        // Check that course was created
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course with Live Session',
            'has_live_session' => true,
        ]);
    }

    public function test_datetime_combination_with_date_only()
    {
        $this->actingAs($this->user);

        // Use a future date
        $futureDate = now()->addDays(5)->format('Y-m-d');

        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course with Date Only',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Test Meeting',
            'meeting_description' => 'Test meeting description',
            'meeting_start_date' => $futureDate,
            // No time provided - should default to 09:00
            'meeting_duration' => '60',
        ]);

        // Should redirect successfully
        $response->assertRedirect(route('admin.courses.index'));
        $response->assertSessionHas('success');
    }

    public function test_datetime_validation_past_date()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course with Past Date',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Test Meeting',
            'meeting_description' => 'Test meeting description',
            'meeting_start_date' => '2020-01-01', // Past date
            'meeting_start_time' => '14:30',
            'meeting_duration' => '60',
        ]);

        // Should redirect back with validation error
        $response->assertRedirect();
        $response->assertSessionHasErrors('meeting_start_date');
    }

    public function test_datetime_validation_separate_fields()
    {
        $this->actingAs($this->user);

        // Test invalid date format
        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course Invalid Date',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Test Meeting',
            'meeting_start_date' => 'invalid-date',
            'meeting_start_time' => '14:30',
            'meeting_duration' => '60',
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors('meeting_start_date');

        // Test invalid time format
        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course Invalid Time',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Test Meeting',
            'meeting_start_date' => '2024-12-25',
            'meeting_start_time' => 'invalid-time',
            'meeting_duration' => '60',
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors('meeting_start_time');
    }

    public function test_course_update_with_datetime_fields()
    {
        $this->actingAs($this->user);

        // First create a course
        $response = $this->post(route('admin.courses.store'), [
            'title' => 'Test Course for Update',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
        ]);

        $course = \App\Models\Course::where('title', 'Test Course for Update')->first();

        // Use a future date for the update
        $futureDate = now()->addDays(10)->format('Y-m-d');

        // Now update it with live session
        $response = $this->put(route('admin.courses.update', $course->id), [
            'title' => 'Updated Test Course',
            'category_id' => $this->category->id,
            'instructor_id' => $this->user->id,
            'status' => 'draft',
            'has_live_session' => '1',
            'meeting_topic' => 'Updated Meeting',
            'meeting_description' => 'Updated meeting description',
            'meeting_start_date' => $futureDate,
            'meeting_start_time' => '16:00',
            'meeting_duration' => '90',
        ]);

        $response->assertRedirect(route('admin.courses.index'));
        $response->assertSessionHas('success');

        // Verify the course was updated
        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
            'title' => 'Updated Test Course',
            'has_live_session' => true,
        ]);
    }
}
