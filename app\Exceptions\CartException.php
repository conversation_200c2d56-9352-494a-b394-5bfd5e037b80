<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

class CartException extends Exception
{
    protected $statusCode;
    protected $errorType;

    public function __construct(
        string $message = 'Cart operation failed',
        int $statusCode = 400,
        string $errorType = 'cart_error',
        \Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        $this->statusCode = $statusCode;
        $this->errorType = $errorType;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(): JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'message' => $this->getMessage(),
            'error_type' => $this->errorType,
        ], $this->statusCode);
    }

    /**
     * Create a course not found exception
     */
    public static function courseNotFound(): self
    {
        return new self(
            'The requested course was not found.',
            404,
            'course_not_found'
        );
    }

    /**
     * Create a course not available exception
     */
    public static function courseNotAvailable(): self
    {
        return new self(
            'This course is not available for purchase.',
            400,
            'course_not_available'
        );
    }

    /**
     * Create an already in cart exception
     */
    public static function alreadyInCart(): self
    {
        return new self(
            'This course is already in your cart.',
            400,
            'already_in_cart'
        );
    }

    /**
     * Create an item not in cart exception
     */
    public static function itemNotInCart(): self
    {
        return new self(
            'The requested item is not in your cart.',
            404,
            'item_not_in_cart'
        );
    }

    /**
     * Create an invalid quantity exception
     */
    public static function invalidQuantity(): self
    {
        return new self(
            'Invalid quantity specified.',
            400,
            'invalid_quantity'
        );
    }

    /**
     * Create a cart limit exceeded exception
     */
    public static function cartLimitExceeded(): self
    {
        return new self(
            'You have reached the maximum number of items in your cart.',
            400,
            'cart_limit_exceeded'
        );
    }

    /**
     * Create an empty cart exception
     */
    public static function emptyCart(): self
    {
        return new self(
            'Your cart is empty.',
            400,
            'empty_cart'
        );
    }
}
