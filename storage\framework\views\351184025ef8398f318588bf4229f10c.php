<div class="cart-modal-content">
    <?php if(empty($cartItems)): ?>
        <div class="cart-empty text-center py-4">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5>Your cart is empty</h5>
            <p class="text-muted">Add some courses to get started!</p>
            <a href="<?php echo e(route('visitors.courses')); ?>" class="btn btn-primary btn-sm">Browse Courses</a>
        </div>
    <?php else: ?>
        <div class="cart-items">
            <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="cart-item d-flex align-items-center mb-3 pb-3 border-bottom" data-course-id="<?php echo e($item['id']); ?>">
                    <div class="cart-item-thumb me-3">
                        <?php if($item['thumbnail']): ?>
                            <img src="<?php echo e(asset('storage/' . $item['thumbnail'])); ?>" alt="<?php echo e($item['title']); ?>" 
                                 class="rounded" style="width: 60px; height: 45px; object-fit: cover;">
                        <?php else: ?>
                            <img src="<?php echo e(asset('frontend/img/course/course_thumb01.jpg')); ?>" alt="<?php echo e($item['title']); ?>" 
                                 class="rounded" style="width: 60px; height: 45px; object-fit: cover;">
                        <?php endif; ?>
                    </div>
                    <div class="cart-item-content flex-grow-1">
                        <h6 class="cart-item-title mb-1">
                            <a href="<?php echo e(route('course.show', $item['slug'])); ?>" class="text-decoration-none">
                                <?php echo e(Str::limit($item['title'], 40)); ?>

                            </a>
                        </h6>
                        <small class="text-muted">By <?php echo e($item['instructor']); ?></small>
                        <div class="cart-item-price mt-1">
                            <?php if($item['is_free']): ?>
                                <span class="text-success fw-bold">Free</span>
                            <?php else: ?>
                                <span class="text-primary fw-bold">KES <?php echo e(number_format($item['price'], 2)); ?></span>
                            <?php endif; ?>
                            <?php if($item['quantity'] > 1): ?>
                                <small class="text-muted"> x <?php echo e($item['quantity']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="cart-item-actions">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-item-modal" 
                                data-course-id="<?php echo e($item['id']); ?>" title="Remove from cart">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="cart-summary mt-3 pt-3 border-top">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">Total Courses:</span>
                <span class="badge bg-primary"><?php echo e($cartSummary['item_count']); ?></span>
            </div>
            
            <?php if($cartSummary['free_courses'] > 0): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Free Courses:</span>
                    <span class="text-success"><?php echo e($cartSummary['free_courses']); ?></span>
                </div>
            <?php endif; ?>
            
            <?php if($cartSummary['paid_courses'] > 0): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Paid Courses:</span>
                    <span><?php echo e($cartSummary['paid_courses']); ?></span>
                </div>
            <?php endif; ?>

            <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="fw-bold fs-5">Total:</span>
                <span class="fw-bold fs-5 text-primary"><?php echo e($cartSummary['total_formatted']); ?></span>
            </div>

            <div class="cart-actions d-grid gap-2">
                <a href="<?php echo e(route('cart.index')); ?>" class="btn btn-outline-primary">View Cart</a>
                <?php if($cartSummary['total'] > 0): ?>
                    <button type="button" class="btn btn-primary proceed-checkout-modal">
                        Proceed to Checkout
                    </button>
                <?php else: ?>
                    <button type="button" class="btn btn-success" disabled>
                        All courses are free
                    </button>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.cart-modal-content {
    max-height: 400px;
    overflow-y: auto;
}

.cart-item-thumb img {
    transition: transform 0.2s;
}

.cart-item-thumb img:hover {
    transform: scale(1.05);
}

.cart-item-title a {
    color: #333;
    font-size: 14px;
    line-height: 1.3;
}

.cart-item-title a:hover {
    color: #007bff;
}

.cart-item-price {
    font-size: 13px;
}

.remove-item-modal {
    padding: 0.25rem 0.5rem;
    font-size: 12px;
}

.cart-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.cart-actions .btn {
    font-size: 14px;
    padding: 8px 16px;
}

.cart-empty i {
    opacity: 0.5;
}
</style>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/cart/modal.blade.php ENDPATH**/ ?>