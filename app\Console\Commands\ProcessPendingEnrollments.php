<?php

namespace App\Console\Commands;

use App\Models\Cmpesa;
use App\Services\EnrollmentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessPendingEnrollments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'enrollments:process-pending {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending enrollments for paid transactions that haven\'t created enrollments yet';

    protected EnrollmentService $enrollmentService;

    public function __construct(EnrollmentService $enrollmentService)
    {
        parent::__construct();
        $this->enrollmentService = $enrollmentService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing pending enrollments...');
        
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Get paid transactions that haven't created enrollments yet
        $pendingTransactions = Cmpesa::paid()
            ->enrollmentNotCreated()
            ->whereNotNull('user_id')
            ->whereNotNull('course_ids')
            ->get();

        if ($pendingTransactions->isEmpty()) {
            $this->info('No pending enrollments found.');
            return 0;
        }

        $this->info("Found {$pendingTransactions->count()} transactions with pending enrollments.");

        $processed = 0;
        $failed = 0;

        foreach ($pendingTransactions as $transaction) {
            $this->line("Processing transaction ID: {$transaction->id} (Request ID: {$transaction->transaction_request_id})");
            
            if (empty($transaction->course_ids)) {
                $this->warn("  - Skipping: No course IDs found");
                continue;
            }

            $this->line("  - User ID: {$transaction->user_id}");
            $this->line("  - Course IDs: " . implode(', ', $transaction->course_ids));
            $this->line("  - Amount: KES " . number_format($transaction->amount, 2));

            if ($isDryRun) {
                $this->info("  - Would create enrollments for " . count($transaction->course_ids) . " course(s)");
                $processed++;
                continue;
            }

            try {
                // Create enrollments
                $result = $this->enrollmentService->createEnrollments($transaction->user_id, $transaction->course_ids);

                if ($result['status'] === 'success') {
                    // Mark enrollment as created
                    $transaction->update(['enrollment_created' => true]);

                    $this->info("  - ✅ Successfully enrolled in " . count($result['enrolled_courses']) . " course(s)");
                    
                    if (!empty($result['skipped_courses'])) {
                        $this->warn("  - ⚠️  Skipped " . count($result['skipped_courses']) . " course(s) (already enrolled or not found)");
                    }

                    $processed++;
                } else {
                    $this->error("  - ❌ Failed: " . $result['message']);
                    $failed++;
                }

            } catch (\Exception $e) {
                $this->error("  - ❌ Exception: " . $e->getMessage());
                Log::error('Command enrollment creation failed', [
                    'transaction_id' => $transaction->id,
                    'error' => $e->getMessage(),
                ]);
                $failed++;
            }

            $this->line('');
        }

        // Summary
        $this->info('=== SUMMARY ===');
        $this->info("Total transactions found: {$pendingTransactions->count()}");
        $this->info("Successfully processed: {$processed}");
        
        if ($failed > 0) {
            $this->error("Failed: {$failed}");
        }

        if ($isDryRun) {
            $this->warn('This was a dry run. Use the command without --dry-run to actually process enrollments.');
        }

        return 0;
    }
}
