<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class HomepageContentSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            // Hero Section
            ['key' => 'hero_title', 'value' => 'Master Professional Skills with CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 1],
            ['key' => 'hero_description', 'value' => 'Transform your career with industry-recognized CPD courses. Learn from expert instructors, earn valuable certifications, and advance your professional development at your own pace.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 2],
            ['key' => 'hero_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 3],
            ['key' => 'hero_button_text', 'value' => 'Explore Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 4],
            
            // About Section
            ['key' => 'about_subtitle', 'value' => 'Get To Know About Us', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 10],
            ['key' => 'about_title', 'value' => 'Empowering Minds, Shaping Futures', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 11],
            ['key' => 'about_description', 'value' => 'We\'re committed to providing high-quality, accessible professional development that fits your schedule. Our platform combines cutting-edge technology with expert instruction to deliver an unparalleled learning experience.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 12],
            ['key' => 'about_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 13],
            ['key' => 'about_button_text', 'value' => 'Start Free Trial', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 14],
            
            // Features Section
            ['key' => 'features_subtitle', 'value' => 'Why Choose Us', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 20],
            ['key' => 'features_title', 'value' => 'Learn with Confidence', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 21],
            ['key' => 'features_description', 'value' => 'Experience the best online learning platform with features designed for your success', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 22],
            
            // Testimonials Section
            ['key' => 'testimonials_subtitle', 'value' => 'Student Success Stories', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 30],
            ['key' => 'testimonials_title', 'value' => 'What Our Students Say', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 31],
            ['key' => 'testimonials_description', 'value' => 'Hear from professionals who transformed their careers with our CPD programs', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 32],
            
            // CTA Section
            ['key' => 'cta_title', 'value' => 'Ready to Start Your Learning Journey?', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 40],
            ['key' => 'cta_description', 'value' => 'Join thousands of professionals who have advanced their careers with our CPD programs. Start learning today and unlock your potential!', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 41],
            ['key' => 'cta_button1_text', 'value' => 'Browse Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 42],
            ['key' => 'cta_button2_text', 'value' => 'Learn More', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 43],
            
            // FAQ Section
            ['key' => 'faq_subtitle', 'value' => 'Got Questions?', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 50],
            ['key' => 'faq_title', 'value' => 'Frequently Asked Questions', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 51],
            ['key' => 'faq_description', 'value' => 'Find answers to common questions about our CPD programs and learning platform', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 52],
            
            // Newsletter Section
            ['key' => 'newsletter_title', 'value' => 'Stay Updated with New Courses & Learning Tips', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 60],
            ['key' => 'newsletter_description', 'value' => 'Get the latest updates on new CPD programs, industry insights, and exclusive learning resources delivered to your inbox.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 61],
            ['key' => 'newsletter_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 62],
            ['key' => 'newsletter_button_text', 'value' => 'Subscribe Now', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 63],
            
            // Instructor Section
            ['key' => 'instructor_subtitle', 'value' => 'Skilled Introduce', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 70],
            ['key' => 'instructor_title', 'value' => 'Our Top Class & Expert Instructors in One Place', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 71],
            ['key' => 'instructor_description', 'value' => 'Combines the ideas of empowered learning and top-tier instruction for students. Emphasizes both instructor expertise', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 72],
            ['key' => 'instructor_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 73],
            ['key' => 'instructor_button_text', 'value' => 'See All Instructors', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 74],
            
            // Colors
            ['key' => 'primary_color', 'value' => '#5751e1', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 100],
            ['key' => 'secondary_color', 'value' => '#3fdacf', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 101],
            ['key' => 'accent_color', 'value' => '#ffc224', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 102],
            
            // Stats
            ['key' => 'stats_students', 'value' => '25K', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 110],
            ['key' => 'stats_cpd_programs', 'value' => '10', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 111],
            ['key' => 'stats_satisfaction', 'value' => '99%', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 112],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}