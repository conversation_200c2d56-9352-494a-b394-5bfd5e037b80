@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">My Assessments</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Assessments</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle mb-0">
                                    <thead>
                                        <tr>
                                            <th>Assessment</th>
                                            <th>Course</th>
                                            <th>Type</th>
                                            <th>Score</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($assessments as $assessment)
                                        <tr>
                                            <td>
                                                <h5 class="font-size-14 mb-1">{{ $assessment->title }}</h5>
                                                <p class="text-muted mb-0">Attempts: {{ $assessment->attempts }}</p>
                                            </td>
                                            <td>{{ $assessment->course->title }}</td>
                                            <td>
                                                <span class="badge badge-soft-info">{{ ucfirst($assessment->type) }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        <h5 class="font-size-14 mb-0">{{ $assessment->score }}/{{ $assessment->max_score }}</h5>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar {{ $assessment->passed ? 'bg-success' : 'bg-warning' }}" 
                                                                 role="progressbar" 
                                                                 style="width: {{ $assessment->score_percentage }}%" 
                                                                 aria-valuenow="{{ $assessment->score_percentage }}" 
                                                                 aria-valuemin="0" 
                                                                 aria-valuemax="100"></div>
                                                        </div>
                                                        <span class="text-muted font-size-12">{{ $assessment->score_percentage }}%</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($assessment->passed)
                                                    <span class="badge badge-soft-success">Passed</span>
                                                @else
                                                    <span class="badge badge-soft-danger">Failed</span>
                                                @endif
                                            </td>
                                            <td>{{ $assessment->completed_at->format('M d, Y') }}</td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <p class="text-muted mb-0">No assessments completed yet.</p>
                                                <a href="{{ route('user.courses.enrolled') }}" class="btn btn-primary btn-sm mt-2">View Enrolled Courses</a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            
                            @if($assessments->hasPages())
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="pagination-wrap hstack gap-2 justify-content-center">
                                        {{ $assessments->links() }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection