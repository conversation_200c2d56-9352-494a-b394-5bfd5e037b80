<?php

return [
    /*
    |--------------------------------------------------------------------------
    | M-Pesa Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for M-Pesa integration.
    | You can get these credentials from Safaricom Developer Portal.
    |
    */

    'consumer_key' => env('MPESA_CONSUMER_KEY', ''),
    'consumer_secret' => env('MPESA_CONSUMER_SECRET', ''),
    'passkey' => env('MPESA_PASSKEY', ''),
    'shortcode' => env('MPESA_SHORTCODE', '174379'),

    /*
    |--------------------------------------------------------------------------
    | Environment Configuration
    |--------------------------------------------------------------------------
    |
    | Set to 'sandbox' for testing or 'production' for live transactions
    |
    */
    'environment' => env('MPESA_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | API URLs
    |--------------------------------------------------------------------------
    |
    | Base URLs for M-Pesa API endpoints
    |
    */
    'base_url' => env('MPESA_ENVIRONMENT', 'sandbox') === 'production'
        ? 'https://api.safaricom.co.ke'
        : 'https://sandbox.safaricom.co.ke',

    /*
    |--------------------------------------------------------------------------
    | Callback URLs
    |--------------------------------------------------------------------------
    |
    | URLs for M-Pesa to send payment notifications
    |
    */
    'callback_url' => env('MPESA_CALLBACK_URL', env('APP_URL') . '/checkout/mpesa/callback'),
    'result_url' => env('MPESA_RESULT_URL', env('APP_URL') . '/checkout/mpesa/result'),
    'timeout_url' => env('MPESA_TIMEOUT_URL', env('APP_URL') . '/checkout/mpesa/timeout'),

    /*
    |--------------------------------------------------------------------------
    | Transaction Types
    |--------------------------------------------------------------------------
    |
    | Available M-Pesa transaction types
    |
    */
    'transaction_types' => [
        'customer_paybill' => 'CustomerPayBillOnline',
        'customer_buygoods' => 'CustomerBuyGoodsOnline',
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Transaction Type
    |--------------------------------------------------------------------------
    |
    | Default transaction type for STK Push
    |
    */
    'default_transaction_type' => 'CustomerPayBillOnline',

    /*
    |--------------------------------------------------------------------------
    | Currency
    |--------------------------------------------------------------------------
    |
    | Default currency for M-Pesa transactions (KES)
    |
    */
    'currency' => 'KES',

    /*
    |--------------------------------------------------------------------------
    | Minimum and Maximum Amounts
    |--------------------------------------------------------------------------
    |
    | Transaction limits for M-Pesa payments
    |
    */
    'min_amount' => 1,
    'max_amount' => 70000,

    /*
    |--------------------------------------------------------------------------
    | Timeout Settings
    |--------------------------------------------------------------------------
    |
    | Timeout settings for M-Pesa API requests
    |
    */
    'timeout' => 30, // seconds
    'connect_timeout' => 10, // seconds

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable/disable logging for M-Pesa transactions
    |
    */
    'logging' => [
        'enabled' => env('MPESA_LOGGING_ENABLED', true),
        'level' => env('MPESA_LOGGING_LEVEL', 'info'),
        'channel' => env('MPESA_LOGGING_CHANNEL', 'daily'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Cache settings for M-Pesa access tokens
    |
    */
    'cache' => [
        'prefix' => 'mpesa_',
        'token_ttl' => 3600, // 1 hour in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Validation rules for M-Pesa phone numbers and amounts
    |
    */
    'validation' => [
        'phone_regex' => '/^254[0-9]{9}$/',
        'amount_decimal_places' => 2,
    ],

    /*
    |--------------------------------------------------------------------------
    | Test Credentials (Sandbox)
    |--------------------------------------------------------------------------
    |
    | Default test credentials for sandbox environment
    | DO NOT use these in production!
    |
    */
    'test' => [
        'consumer_key' => 'your_test_consumer_key',
        'consumer_secret' => 'your_test_consumer_secret',
        'passkey' => 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',
        'shortcode' => '174379',
        'test_phone' => '254708374149',
    ],
];
