<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Services\CartService;
use App\Http\Requests\CartRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class CartController extends Controller
{
    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Display the cart page
     */
    public function index(): View
    {
        $cartItems = $this->cartService->getItems();
        $cartSummary = $this->cartService->getSummary();

        // Validate cart items
        $validation = $this->cartService->validateCart();

        return view('cart.index', compact('cartItems', 'cartSummary', 'validation'));
    }

    /**
     * Add course to cart (AJAX)
     */
    public function add(CartRequest $request, int $courseId): JsonResponse
    {
        try {
            // Use the validated course from the request
            $course = $request->getCourse();

            if (!$course) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Course not found.',
                ], 404);
            }

            // Check if course can be added to cart
            $canAdd = $request->canAddToCart();
            if (!$canAdd['can_add']) {
                return response()->json([
                    'status' => 'error',
                    'message' => $canAdd['reason'],
                ], 400);
            }

            // Check if already in cart
            if ($this->cartService->hasItem($courseId)) {
                return response()->json([
                    'status' => 'info',
                    'message' => 'Course is already in your cart!',
                    'cart_count' => $this->cartService->getItemCount(),
                ]);
            }

            $quantity = $request->validated()['quantity'] ?? 1;
            $result = $this->cartService->add($course, $quantity);

            // Log successful cart addition
            Log::info('Course added to cart', [
                'course_id' => $courseId,
                'course_title' => $course->title,
                'quantity' => $quantity,
                'user_ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to add course to cart', [
                'course_id' => $courseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add course to cart. Please try again.',
            ], 500);
        }
    }

    /**
     * Remove course from cart (AJAX)
     */
    public function remove(int $courseId): JsonResponse
    {
        try {
            $result = $this->cartService->remove($courseId);

            // Log cart removal
            Log::info('Course removed from cart', [
                'course_id' => $courseId,
            ]);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Failed to remove course from cart', [
                'course_id' => $courseId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to remove course from cart.',
            ], 500);
        }
    }

    /**
     * Update cart item quantity (AJAX)
     */
    public function updateQuantity(Request $request, int $courseId): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:0|max:10',
        ]);

        try {
            $quantity = $request->input('quantity');
            $result = $this->cartService->updateQuantity($courseId, $quantity);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update cart.',
            ], 500);
        }
    }

    /**
     * Clear entire cart (AJAX)
     */
    public function clear(): JsonResponse
    {
        try {
            $result = $this->cartService->clear();
            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to clear cart.',
            ], 500);
        }
    }

    /**
     * Get cart contents (AJAX)
     */
    public function getCart(): JsonResponse
    {
        try {
            $cartItems = $this->cartService->getItems();
            $cartSummary = $this->cartService->getSummary();

            return response()->json([
                'status' => 'success',
                'items' => $cartItems,
                'summary' => $cartSummary,
                'cart_count' => $this->cartService->getItemCount(),
                'cart_total' => $this->cartService->getFormattedTotal(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get cart contents.',
            ], 500);
        }
    }

    /**
     * Get cart count for header display (AJAX)
     */
    public function getCount(): JsonResponse
    {
        try {
            return response()->json([
                'status' => 'success',
                'cart_count' => $this->cartService->getItemCount(),
                'cart_total' => $this->cartService->getFormattedTotal(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'cart_count' => 0,
            ]);
        }
    }

    /**
     * Validate cart items (AJAX)
     */
    public function validate(): JsonResponse
    {
        try {
            $validation = $this->cartService->validateCart();
            $validation['cart_count'] = $this->cartService->getItemCount();
            $validation['cart_total'] = $this->cartService->getFormattedTotal();

            return response()->json($validation);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to validate cart.',
            ], 500);
        }
    }

    /**
     * Get cart modal content (AJAX)
     */
    public function getModal(): JsonResponse
    {
        try {
            $cartItems = $this->cartService->getItems();
            $cartSummary = $this->cartService->getSummary();

            $html = view('cart.modal', compact('cartItems', 'cartSummary'))->render();

            return response()->json([
                'status' => 'success',
                'html' => $html,
                'cart_count' => $this->cartService->getItemCount(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to load cart modal.',
            ], 500);
        }
    }
}
