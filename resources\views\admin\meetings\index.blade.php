@extends('admin.dashboard')

@section('styles')
<!-- DataTables -->
<link href="{{ asset('assets/libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
<link href="{{ asset('assets/libs/datatables.net-buttons-bs4/css/buttons.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
<link href="{{ asset('assets/libs/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Zoom Meetings</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Meetings</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title">Meeting Management</h4>
                                    <p class="card-title-desc">Manage all Zoom meetings and track attendance.</p>
                                </div>
                                <div>
                                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
                                        <i class="uil-plus me-1"></i> Create Course with Meeting
                                    </a>
                                </div>
                            </div>

                            @if (session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="search-box">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="searchInput" placeholder="Search meetings...">
                                            <i class="uil-search search-icon"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="statusFilter">
                                        <option value="">All Status</option>
                                        <option value="scheduled">Scheduled</option>
                                        <option value="started">Started</option>
                                        <option value="ended">Ended</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="timeFilter">
                                        <option value="">All Time</option>
                                        <option value="today">Today</option>
                                        <option value="upcoming">Upcoming</option>
                                        <option value="past">Past</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="recordingFilter">
                                        <option value="">All Recordings</option>
                                        <option value="available">Available</option>
                                        <option value="not_available">Not Available</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" onclick="refreshMeetings()">
                                        <i class="uil-refresh me-1"></i> Refresh
                                    </button>
                                </div>
                            </div>

                            <!-- Meeting Stats -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card border-0 bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-video display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meetings->total() ?? 0 }}</h5>
                                                    <p class="mb-0">Total Meetings</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-calendar-alt display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meetings->where('status', 'scheduled')->count() ?? 0 }}</h5>
                                                    <p class="mb-0">Scheduled</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-play-circle display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meetings->where('status', 'started')->count() ?? 0 }}</h5>
                                                    <p class="mb-0">Active</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-record-audio display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $meetings->where('recording_available', true)->count() ?? 0 }}</h5>
                                                    <p class="mb-0">Recordings</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover" id="meetingsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Meeting</th>
                                            <th>Course</th>
                                            <th>Date & Time</th>
                                            <th>Duration</th>
                                            <th>Status</th>
                                            <th>Attendance</th>
                                            <th>Recording</th>
                                            <th>Created By</th>
                                            <th style="width: 120px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($meetings as $meeting)
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1">{{ $meeting->topic }}</h6>
                                                    <p class="text-muted mb-0 small">{{ Str::limit($meeting->description, 50) }}</p>
                                                    <div class="mt-1">
                                                        <span class="badge bg-secondary small">ID: {{ $meeting->zoom_meeting_id }}</span>
                                                        @if($meeting->password)
                                                            <span class="badge bg-warning small"><i class="uil-lock"></i> Protected</span>
                                                        @endif
                                                        @if($meeting->attendance_required_for_completion)
                                                            <span class="badge bg-info small"><i class="uil-check"></i> Required</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        @if($meeting->course->thumbnail)
                                                            <img src="{{ asset('storage/' . $meeting->course->thumbnail) }}" alt="{{ $meeting->course->title }}"
                                                                 class="rounded" style="width: 40px; height: 30px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                                 style="width: 40px; height: 30px;">
                                                                <i class="uil-book-open text-muted"></i>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0 small">{{ Str::limit($meeting->course->title, 30) }}</h6>
                                                        <p class="text-muted mb-0 small">{{ $meeting->course->category->name ?? 'Uncategorized' }}</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0 small">{{ $meeting->start_time->format('M d, Y') }}</h6>
                                                    <p class="text-muted mb-0 small">{{ $meeting->start_time->format('g:i A') }}</p>
                                                    <span class="badge bg-light text-dark small">{{ $meeting->timezone }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $meeting->formatted_duration }}</span>
                                                @if($meeting->minimum_attendance_minutes)
                                                    <br><small class="text-muted">Min: {{ $meeting->minimum_attendance_minutes }}m</small>
                                                @endif
                                            </td>
                                            <td>
                                                @switch($meeting->status)
                                                    @case('scheduled')
                                                        <span class="badge bg-primary">
                                                            <i class="uil-calendar-alt me-1"></i>Scheduled
                                                        </span>
                                                        @break
                                                    @case('started')
                                                        <span class="badge bg-success">
                                                            <i class="uil-play-circle me-1"></i>Live
                                                        </span>
                                                        @break
                                                    @case('ended')
                                                        <span class="badge bg-secondary">
                                                            <i class="uil-stop-circle me-1"></i>Ended
                                                        </span>
                                                        @break
                                                    @case('cancelled')
                                                        <span class="badge bg-danger">
                                                            <i class="uil-times-circle me-1"></i>Cancelled
                                                        </span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-light text-dark">Unknown</span>
                                                @endswitch
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="badge bg-success">{{ $meeting->total_attendees }} attended</span>
                                                    <br><small class="text-muted">{{ $meeting->qualified_attendees }} qualified</small>
                                                </div>
                                            </td>
                                            <td>
                                                @if($meeting->recording_available)
                                                    <span class="badge bg-success">
                                                        <i class="uil-record-audio me-1"></i>Available
                                                    </span>
                                                @else
                                                    <span class="badge bg-light text-muted">
                                                        <i class="uil-times me-1"></i>Not Available
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        <img src="{{ $meeting->creator->avatar ?? asset('assets/images/users/avatar-default.jpg') }}"
                                                             alt="{{ $meeting->creator->name }}"
                                                             class="rounded-circle" style="width: 24px; height: 24px;">
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0 small">{{ $meeting->creator->name }}</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-light btn-sm dropdown-toggle" type="button"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('admin.meetings.show', $meeting->id) }}">
                                                                <i class="uil-eye me-2"></i>View Details
                                                            </a>
                                                        </li>
                                                        @if($meeting->join_url)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ $meeting->join_url }}" target="_blank">
                                                                <i class="uil-external-link-alt me-2"></i>Join Meeting
                                                            </a>
                                                        </li>
                                                        @endif
                                                        @if($meeting->start_url)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ $meeting->start_url }}" target="_blank">
                                                                <i class="uil-play me-2"></i>Start Meeting
                                                            </a>
                                                        </li>
                                                        @endif
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <form action="{{ route('admin.meetings.sync-attendance', $meeting->id) }}" method="POST" style="display: inline;">
                                                                @csrf
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="uil-refresh me-2"></i>Sync Attendance
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <li>
                                                            <form action="{{ route('admin.meetings.update-status', $meeting->id) }}" method="POST" style="display: inline;">
                                                                @csrf
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="uil-sync me-2"></i>Update Status
                                                                </button>
                                                            </form>
                                                        </li>
                                                        @if($meeting->recording_available && $meeting->recording_url)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ $meeting->recording_url }}" target="_blank">
                                                                <i class="uil-record-audio me-2"></i>View Recording
                                                            </a>
                                                        </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="uil-video display-4 text-muted mb-3"></i>
                                                    <h5 class="text-muted">No meetings found</h5>
                                                    <p class="text-muted">Create courses with live sessions to see meetings here</p>
                                                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
                                                        <i class="uil-plus me-1"></i> Create Course with Meeting
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            {{-- Pagination --}}
                            @if($meetings->hasPages())
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <p class="text-muted">
                                            Showing {{ $meetings->firstItem() }} to {{ $meetings->lastItem() }}
                                            of {{ $meetings->total() }} results
                                        </p>
                                    </div>
                                    <div>
                                        {{ $meetings->links() }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>
@endsection

@section('scripts')
<style>
    .search-box .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #adb5bd;
    }
</style>

<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTable();
    });

    // Filter functionality
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTable();
    });

    document.getElementById('timeFilter').addEventListener('change', function() {
        filterTable();
    });

    document.getElementById('recordingFilter').addEventListener('change', function() {
        filterTable();
    });

    function filterTable() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const timeFilter = document.getElementById('timeFilter').value;
        const recordingFilter = document.getElementById('recordingFilter').value;
        const table = document.getElementById('meetingsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            if (row.cells.length < 9) continue; // Skip empty state row

            const meetingTitle = row.cells[0].textContent.toLowerCase();
            const courseTitle = row.cells[1].textContent.toLowerCase();
            const status = row.cells[4].textContent.toLowerCase();
            const recording = row.cells[6].textContent.toLowerCase();

            let showRow = true;

            // Search filter
            if (searchTerm && !meetingTitle.includes(searchTerm) && !courseTitle.includes(searchTerm)) {
                showRow = false;
            }

            // Status filter
            if (statusFilter && !status.includes(statusFilter)) {
                showRow = false;
            }

            // Recording filter
            if (recordingFilter === 'available' && !recording.includes('available')) {
                showRow = false;
            } else if (recordingFilter === 'not_available' && recording.includes('available')) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        }
    }

    function refreshMeetings() {
        window.location.reload();
    }
</script>
@endsection
