<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assessment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'title',
        'type',
        'score',
        'max_score',
        'passed',
        'completed_at',
        'attempts',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
        'passed' => 'boolean',
        'score' => 'decimal:2',
        'max_score' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function getScorePercentageAttribute(): int
    {
        return $this->max_score > 0 ? (int)(($this->score / $this->max_score) * 100) : 0;
    }
}