<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('checkout-form');
        const paymentMethodInputs = document.querySelectorAll('input[name="payment_method"]');
        const phoneNumberInput = document.getElementById('phone_number');
        const placeOrderBtn = document.getElementById('place-order-btn');

        // Handle payment method changes
        paymentMethodInputs.forEach(input => {
            input.addEventListener('change', function() {
                // Hide all payment details
                document.querySelectorAll('.payment-details').forEach(detail => {
                    detail.style.display = 'none';
                });

                // Show relevant payment details
                if (this.value === 'mpesa') {
                    document.querySelector('.mpesa-details').style.display = 'block';
                    phoneNumberInput.required = true;
                } else if (this.value === 'bank_transfer') {
                    document.querySelector('.bank-details').style.display = 'block';
                    phoneNumberInput.required = false;
                } else {
                    phoneNumberInput.required = false;
                }
            });
        });

        // Format phone number input
        phoneNumberInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');

            if (value.startsWith('0')) {
                value = '254' + value.substring(1);
            } else if (value.startsWith('7') || value.startsWith('1')) {
                value = '254' + value;
            }

            this.value = value;
        });

        // Handle form submission
        form.addEventListener('submit', function(e) {
            // Allow default form submission to backend for processing and redirecting
            // No modal or ajax submission for now
        });
    });
</script>
