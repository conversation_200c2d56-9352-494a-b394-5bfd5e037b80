<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class MeetingAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'zoom_meeting_id',
        'user_id',
        'participant_uuid',
        'participant_user_id',
        'name',
        'email',
        'joined_at',
        'left_at',
        'duration_minutes',
        'attended_full_session',
        'status',
        'zoom_data',
    ];

    protected $casts = [
        'joined_at' => 'datetime',
        'left_at' => 'datetime',
        'attended_full_session' => 'boolean',
        'duration_minutes' => 'integer',
        'zoom_data' => 'array',
    ];

    // Relationships
    public function zoomMeeting(): BelongsTo
    {
        return $this->belongsTo(ZoomMeeting::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeQualified($query)
    {
        return $query->where('attended_full_session', true);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'in_meeting');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'left');
    }

    // Accessors & Mutators
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }
        
        return $minutes . ' minutes';
    }

    public function getAttendancePercentageAttribute(): float
    {
        if (!$this->zoomMeeting) {
            return 0;
        }

        return round(($this->duration_minutes / $this->zoomMeeting->duration) * 100, 2);
    }

    public function getIsQualifiedAttribute(): bool
    {
        return $this->attended_full_session;
    }

    // Methods
    public function calculateDuration(): void
    {
        if ($this->joined_at && $this->left_at) {
            $this->duration_minutes = $this->joined_at->diffInMinutes($this->left_at);
            $this->checkAttendanceRequirement();
        }
    }

    public function checkAttendanceRequirement(): void
    {
        if ($this->zoomMeeting && $this->duration_minutes >= $this->zoomMeeting->minimum_attendance_minutes) {
            $this->attended_full_session = true;
        }
    }

    public function markAsLeft(): void
    {
        $this->left_at = now();
        $this->status = 'left';
        $this->calculateDuration();
        $this->save();
    }

    public function updateStatus(string $status): void
    {
        $this->status = $status;
        
        if ($status === 'in_meeting' && !$this->joined_at) {
            $this->joined_at = now();
        } elseif ($status === 'left' && !$this->left_at) {
            $this->markAsLeft();
        }
        
        $this->save();
    }

    public static function recordAttendance(ZoomMeeting $meeting, User $user, array $participantData): self
    {
        return self::updateOrCreate([
            'zoom_meeting_id' => $meeting->id,
            'user_id' => $user->id,
            'participant_uuid' => $participantData['participant_uuid'] ?? null,
        ], [
            'participant_user_id' => $participantData['participant_user_id'] ?? null,
            'name' => $participantData['name'] ?? $user->name,
            'email' => $participantData['email'] ?? $user->email,
            'joined_at' => isset($participantData['joined_at']) ? Carbon::parse($participantData['joined_at']) : now(),
            'status' => $participantData['status'] ?? 'joined',
            'zoom_data' => $participantData,
        ]);
    }
}
