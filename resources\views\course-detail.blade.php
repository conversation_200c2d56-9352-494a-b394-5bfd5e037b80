@extends('welcome')
@section('content')
<main class="main-area fix">
    <!-- breadcrumb-area -->
    <section class="breadcrumb__area breadcrumb__bg" data-background="{{ asset('frontend/img/bg/breadcrumb_bg.jpg') }}">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="breadcrumb__content">
                        <h3 class="title">{{ $course->title }}</h3>
                        <nav class="breadcrumb">
                            <span property="itemListElement" typeof="ListItem">
                                <a href="{{ route('home') }}">Home</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">
                                <a href="{{ route('visitors.courses') }}">Courses</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">{{ $course->title }}</span>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <div class="breadcrumb__shape-wrap">
            <img src="{{ asset('frontend/img/others/breadcrumb_shape01.svg') }}" alt="img" class="alltuchtopdown">
            <img src="{{ asset('frontend/img/others/breadcrumb_shape02.svg') }}" alt="img" data-aos="fade-right" data-aos-delay="300">
            <img src="{{ asset('frontend/img/others/breadcrumb_shape03.svg') }}" alt="img" data-aos="fade-up" data-aos-delay="400">
            <img src="{{ asset('frontend/img/others/breadcrumb_shape04.svg') }}" alt="img" data-aos="fade-down-left" data-aos-delay="400">
            <img src="{{ asset('frontend/img/others/breadcrumb_shape05.svg') }}" alt="img" data-aos="fade-left" data-aos-delay="400">
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <!-- courses-details-area -->
    <section class="courses__details-area section-py-120">
        <div class="container">
            <div class="row">
                <div class="col-xl-9 col-lg-8">
                    <div class="courses__details-thumb">
                        @if($course->thumbnail)
                            <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}">
                        @else
                            <img src="{{ asset('frontend/img/course/course_details_thumb.jpg') }}" alt="{{ $course->title }}">
                        @endif
                    </div>
                    <div class="courses__details-content">
                        <div class="courses__item-meta list-wrap">
                            <ul class="list-wrap">
                                <li class="courses__item-tag">
                                    <a href="{{ route('visitors.courses') }}?category={{ $course->category->slug }}">{{ $course->category->name }}</a>
                                </li>
                                <li class="avg-rating"><i class="fas fa-star"></i> 5.0 (25 Reviews)</li>
                            </ul>
                        </div>
                        <h2 class="title">{{ $course->title }}</h2>
                        <div class="courses__details-meta">
                            <ul class="list-wrap">
                                <li class="author-two">
                                    <img src="{{ asset('frontend/img/courses/course_author001.png') }}" alt="img">
                                    By <a href="#">{{ $course->instructor->name }}</a>
                                </li>
                                <li><i class="flaticon-mortarboard"></i>2,250 Students</li>
                                <li><i class="flaticon-play-button"></i>{{ $course->duration }} Hours</li>
                                <li><i class="flaticon-calendar"></i>{{ $course->created_at->format('M d, Y') }}</li>
                            </ul>
                        </div>
                        <div class="courses__details-desc">
                            <h4 class="title">Course Description</h4>
                            <p>{{ $course->short_description }}</p>
                            <div class="content">
                                {!! nl2br(e($course->description)) !!}
                            </div>
                        </div>

                        @if($course->learning_objectives)
                        <div class="courses__details-desc">
                            <h4 class="title">What You'll Learn</h4>
                            <div class="content">
                                {!! nl2br(e($course->learning_objectives)) !!}
                            </div>
                        </div>
                        @endif

                        @if($course->requirements)
                        <div class="courses__details-desc">
                            <h4 class="title">Requirements</h4>
                            <div class="content">
                                {!! nl2br(e($course->requirements)) !!}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="col-xl-3 col-lg-4">
                    <aside class="courses__details-sidebar">
                        <div class="courses__cost-wrap">
                            <span class="price">
                                @if($course->is_free)
                                    Free
                                @elseif($course->discount_price && $course->discount_price < $course->price)
                                    <del>KES {{ number_format($course->price, 2) }}</del>
                                    KES {{ number_format($course->discount_price, 2) }}
                                @else
                                    KES {{ number_format($course->price ?? 0, 2) }}
                                @endif
                            </span>
                            @if($course->is_free)
                                <a href="#" class="btn btn-two arrow-btn">Enroll Free <img src="{{ asset('frontend/img/icons/right_arrow.svg') }}" alt="img" class="injectable"></a>
                            @else
                                <a href="javascript:;" class="btn btn-two arrow-btn add-to-cart" data-id="{{ $course->id }}" data-title="{{ $course->title }}">
                                    <span>Add To Cart</span> <img src="{{ asset('frontend/img/icons/right_arrow.svg') }}" alt="img" class="injectable">
                                </a>
                            @endif
                        </div>
                        <div class="courses__information-wrap">
                            <h5 class="title">Course Information</h5>
                            <ul class="list-wrap">
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon01.svg') }}" alt="img" class="injectable">
                                    Instructor
                                    <span>{{ $course->instructor->name }}</span>
                                </li>
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon02.svg') }}" alt="img" class="injectable">
                                    Duration
                                    <span>{{ $course->duration }} Hours</span>
                                </li>
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon03.svg') }}" alt="img" class="injectable">
                                    Lessons
                                    <span>15</span>
                                </li>
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon04.svg') }}" alt="img" class="injectable">
                                    Video
                                    <span>{{ $course->duration }} Hours</span>
                                </li>
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon05.svg') }}" alt="img" class="injectable">
                                    Level
                                    <span>{{ ucfirst($course->difficulty_level ?? 'Beginner') }}</span>
                                </li>
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon06.svg') }}" alt="img" class="injectable">
                                    Language
                                    <span>{{ ucfirst($course->language ?? 'English') }}</span>
                                </li>
                                @if($course->certificate)
                                <li>
                                    <img src="{{ asset('frontend/img/icons/course_icon07.svg') }}" alt="img" class="injectable">
                                    Certificate
                                    <span>Yes</span>
                                </li>
                                @endif
                            </ul>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </section>
    <!-- courses-details-area-end -->

    @if($relatedCourses->count() > 0)
    <!-- courses-area -->
    <section class="courses-area section-pt-120 section-pb-90">
        <div class="container">
            <div class="section__title-wrap">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="section__title text-center mb-40">
                            <span class="sub-title">Related Courses</span>
                            <h2 class="title">More Courses in {{ $course->category->name }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row courses__grid-wrap row-cols-1 row-cols-xl-4 row-cols-lg-3 row-cols-md-2 row-cols-sm-1">
                @foreach($relatedCourses as $relatedCourse)
                    <div class="col">
                        <x-course-card :course="$relatedCourse" />
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    <!-- courses-area-end -->
    @endif
</main>
@endsection
