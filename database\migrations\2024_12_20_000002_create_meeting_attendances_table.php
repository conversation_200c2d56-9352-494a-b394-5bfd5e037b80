<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('zoom_meeting_id')->constrained('zoom_meetings')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('participant_uuid')->nullable(); // Zoom participant UUID
            $table->string('participant_user_id')->nullable(); // Zoom user ID
            $table->string('name'); // Name used in meeting
            $table->string('email')->nullable();
            $table->dateTime('joined_at')->nullable();
            $table->dateTime('left_at')->nullable();
            $table->integer('duration_minutes')->default(0);
            $table->boolean('attended_full_session')->default(false); // Met minimum attendance requirement
            $table->enum('status', ['joined', 'left', 'in_meeting'])->default('joined');
            $table->json('zoom_data')->nullable(); // Store additional Zoom participant data
            $table->timestamps();

            // Indexes
            $table->index(['zoom_meeting_id', 'user_id']);
            $table->index(['user_id', 'attended_full_session']);
            $table->index('participant_uuid');
            $table->unique(['zoom_meeting_id', 'user_id', 'participant_uuid'], 'meeting_attendance_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_attendances');
    }
};
