<?php $__env->startSection('content'); ?>
<main class="main-area fix">
    <!-- breadcrumb-area -->
    <section class="breadcrumb__area breadcrumb__bg" data-background="<?php echo e(asset('frontend/img/bg/breadcrumb_bg.jpg')); ?>">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="breadcrumb__content">
                        <h3 class="title">Shopping Cart</h3>
                        <nav class="breadcrumb">
                            <span property="itemListElement" typeof="ListItem">
                                <a href="<?php echo e(url('/')); ?>">Home</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">Shopping Cart</span>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <!-- cart-area -->
    <section class="cart__area section-py-120">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <?php if(isset($validation) && $validation['status'] === 'warning'): ?>
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <?php echo e($validation['message']); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(empty($cartItems)): ?>
                        <div class="cart__empty">
                            <div class="text-center">
                                <img src="<?php echo e(asset('frontend/img/icons/empty-cart.svg')); ?>" alt="Empty Cart" class="mb-4" style="width: 150px;">
                                <h3>Your cart is empty</h3>
                                <p class="mb-4">Looks like you haven't added any courses to your cart yet.</p>
                                <a href="<?php echo e(route('visitors.courses')); ?>" class="btn btn-two">Browse Courses</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="cart__wrapper">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="cart__table-wrap">
                                        <table class="cart__table">
                                            <thead>
                                                <tr>
                                                    <th class="product">Course</th>
                                                    <th class="price">Price</th>
                                                    <th class="quantity">Quantity</th>
                                                    <th class="total">Total</th>
                                                    <th class="remove">Remove</th>
                                                </tr>
                                            </thead>
                                            <tbody id="cart-items">
                                                <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr data-course-id="<?php echo e($item['id']); ?>">
                                                        <td class="product">
                                                            <div class="cart__product">
                                                                <div class="cart__product-thumb">
                                                                    <?php if($item['thumbnail']): ?>
                                                                        <img src="<?php echo e(asset('storage/' . $item['thumbnail'])); ?>" alt="<?php echo e($item['title']); ?>">
                                                                    <?php else: ?>
                                                                        <img src="<?php echo e(asset('frontend/img/course/course_thumb01.jpg')); ?>" alt="<?php echo e($item['title']); ?>">
                                                                    <?php endif; ?>
                                                                </div>
                                                                <div class="cart__product-content">
                                                                    <h4 class="title">
                                                                        <a href="<?php echo e(route('course.show', $item['slug'])); ?>"><?php echo e($item['title']); ?></a>
                                                                    </h4>
                                                                    <span class="instructor">By <?php echo e($item['instructor']); ?></span>
                                                                    <?php if($item['duration']): ?>
                                                                        <span class="duration"><?php echo e($item['duration']); ?> Hours</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="price">
                                                            <?php if($item['is_free']): ?>
                                                                <span class="amount">Free</span>
                                                            <?php else: ?>
                                                                <span class="amount">KES <?php echo e(number_format($item['price'], 2)); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="quantity">
                                                            <div class="cart-plus-minus">
                                                                <input type="text" value="<?php echo e($item['quantity']); ?>" readonly>
                                                            </div>
                                                        </td>
                                                        <td class="total">
                                                            <?php if($item['is_free']): ?>
                                                                <span class="amount">Free</span>
                                                            <?php else: ?>
                                                                <span class="amount item-total">KES <?php echo e(number_format($item['price'] * $item['quantity'], 2)); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="remove">
                                                            <a href="javascript:;" class="remove-item" data-course-id="<?php echo e($item['id']); ?>" title="Remove from cart">
                                                                <i class="fas fa-times"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="cart__actions">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <a href="<?php echo e(route('visitors.courses')); ?>" class="btn btn-outline">Continue Shopping</a>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="button" class="btn btn-outline clear-cart">Clear Cart</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4">
                                    <div class="cart__sidebar">
                                        <div class="cart__summary">
                                            <h4 class="title">Cart Summary</h4>
                                            <ul class="cart__summary-list">
                                                <li>
                                                    <span>Total Courses:</span>
                                                    <span id="total-courses"><?php echo e($cartSummary['item_count']); ?></span>
                                                </li>
                                                <?php if($cartSummary['free_courses'] > 0): ?>
                                                    <li>
                                                        <span>Free Courses:</span>
                                                        <span><?php echo e($cartSummary['free_courses']); ?></span>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if($cartSummary['paid_courses'] > 0): ?>
                                                    <li>
                                                        <span>Paid Courses:</span>
                                                        <span><?php echo e($cartSummary['paid_courses']); ?></span>
                                                    </li>
                                                <?php endif; ?>
                                                <li class="subtotal">
                                                    <span>Subtotal:</span>
                                                    <span id="cart-subtotal"><?php echo e($cartSummary['subtotal_formatted']); ?></span>
                                                </li>
                                                <li class="total">
                                                    <span>Total:</span>
                                                    <span id="cart-total"><?php echo e($cartSummary['total_formatted']); ?></span>
                                                </li>
                                            </ul>

                                            <?php if($cartSummary['total'] > 0): ?>
                                                <div class="cart__checkout">
                                                    <a href="<?php echo e(route('checkout.index')); ?>" class="btn btn-two w-100">
                                                        Proceed to Checkout
                                                    </a>
                                                </div>
                                            <?php else: ?>
                                                <div class="cart__checkout">
                                                    <button type="button" class="btn btn-two w-100" disabled>
                                                        All courses are free - Enroll directly
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <!-- cart-area-end -->
</main>

<style>
.cart__empty {
    padding: 80px 0;
}

.cart__table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

.cart__table th,
.cart__table td {
    padding: 20px 15px;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.cart__table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.cart__product {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cart__product-thumb img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.cart__product-content .title {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.cart__product-content .instructor,
.cart__product-content .duration {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 3px;
}

.cart__summary {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
}

.cart__summary-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.cart__summary-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
}

.cart__summary-list li.total {
    font-weight: 600;
    font-size: 18px;
    border-bottom: none;
    padding-top: 15px;
}

.remove-item {
    color: #dc3545;
    font-size: 18px;
    transition: color 0.3s;
}

.remove-item:hover {
    color: #c82333;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('welcome', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/cart/index.blade.php ENDPATH**/ ?>