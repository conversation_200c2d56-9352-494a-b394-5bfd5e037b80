<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class MpesaService
{
    private string $consumerKey;
    private string $consumerSecret;
    private string $passkey;
    private string $shortcode;
    private string $baseUrl;
    private string $callbackUrl;

    public function __construct()
    {
        $this->consumerKey = config('mpesa.consumer_key');
        $this->consumerSecret = config('mpesa.consumer_secret');
        $this->passkey = config('mpesa.passkey');
        $this->shortcode = config('mpesa.shortcode');
        $this->baseUrl = config('mpesa.base_url');
        $this->callbackUrl = config('mpesa.callback_url');
    }

    /**
     * Get access token from M-Pesa API
     */
    public function getAccessToken(): ?string
    {
        $cacheKey = 'mpesa_access_token';

        // Check if token exists in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $credentials = base64_encode($this->consumerKey . ':' . $this->consumerSecret);

            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . $credentials,
                'Content-Type' => 'application/json',
            ])->get($this->baseUrl . '/oauth/v1/generate?grant_type=client_credentials');

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['access_token'];
                $expiresIn = $data['expires_in'] ?? 3600;

                // Cache token for slightly less than expiry time
                Cache::put($cacheKey, $token, now()->addSeconds($expiresIn - 60));

                return $token;
            } else {
                Log::error('M-Pesa token request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('M-Pesa token request exception', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Initiate STK Push payment
     */
    public function initiateSTKPush(array $params): array
    {
        $token = $this->getAccessToken();

        if (!$token) {
            return [
                'status' => 'error',
                'message' => 'Failed to get M-Pesa access token.',
            ];
        }

        try {
            $timestamp = Carbon::now()->format('YmdHis');
            $password = base64_encode($this->shortcode . $this->passkey . $timestamp);

            $payload = [
                'BusinessShortCode' => $this->shortcode,
                'Password' => $password,
                'Timestamp' => $timestamp,
                'TransactionType' => 'CustomerPayBillOnline',
                'Amount' => (int) $params['amount'],
                'PartyA' => $params['phone_number'],
                'PartyB' => $this->shortcode,
                'PhoneNumber' => $params['phone_number'],
                'CallBackURL' => $this->callbackUrl,
                'AccountReference' => $params['account_reference'],
                'TransactionDesc' => $params['transaction_desc'],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/mpesa/stkpush/v1/processrequest', $payload);

            $data = $response->json();

            if ($response->successful() && isset($data['ResponseCode']) && $data['ResponseCode'] == '0') {
                Log::info('M-Pesa STK Push initiated', [
                    'checkout_request_id' => $data['CheckoutRequestID'],
                    'merchant_request_id' => $data['MerchantRequestID'],
                    'phone_number' => $params['phone_number'],
                    'amount' => $params['amount'],
                ]);

                return [
                    'status' => 'success',
                    'message' => 'STK Push initiated successfully.',
                    'checkout_request_id' => $data['CheckoutRequestID'],
                    'merchant_request_id' => $data['MerchantRequestID'],
                ];
            } else {
                Log::error('M-Pesa STK Push failed', [
                    'response' => $data,
                    'payload' => $payload,
                ]);

                return [
                    'status' => 'error',
                    'message' => $data['errorMessage'] ?? 'STK Push initiation failed.',
                ];
            }
        } catch (\Exception $e) {
            Log::error('M-Pesa STK Push exception', [
                'error' => $e->getMessage(),
                'params' => $params,
            ]);

            return [
                'status' => 'error',
                'message' => 'STK Push request failed.',
            ];
        }
    }

    /**
     * Handle M-Pesa callback
     */
    public function handleCallback(array $callbackData): array
    {
        try {
            Log::info('M-Pesa callback received', $callbackData);

            $stkCallback = $callbackData['Body']['stkCallback'] ?? null;

            if (!$stkCallback) {
                return [
                    'status' => 'error',
                    'message' => 'Invalid callback data.',
                ];
            }

            $resultCode = $stkCallback['ResultCode'];
            $checkoutRequestId = $stkCallback['CheckoutRequestID'];

            if ($resultCode == 0) {
                // Payment successful
                $callbackMetadata = $stkCallback['CallbackMetadata']['Item'] ?? [];
                $mpesaReceiptNumber = null;
                $amount = null;
                $phoneNumber = null;

                foreach ($callbackMetadata as $item) {
                    switch ($item['Name']) {
                        case 'MpesaReceiptNumber':
                            $mpesaReceiptNumber = $item['Value'];
                            break;
                        case 'Amount':
                            $amount = $item['Value'];
                            break;
                        case 'PhoneNumber':
                            $phoneNumber = $item['Value'];
                            break;
                    }
                }

                Log::info('M-Pesa payment successful', [
                    'checkout_request_id' => $checkoutRequestId,
                    'mpesa_receipt_number' => $mpesaReceiptNumber,
                    'amount' => $amount,
                    'phone_number' => $phoneNumber,
                ]);

                return [
                    'status' => 'success',
                    'checkout_request_id' => $checkoutRequestId,
                    'mpesa_receipt_number' => $mpesaReceiptNumber,
                    'amount' => $amount,
                    'phone_number' => $phoneNumber,
                ];
            } else {
                // Payment failed
                $resultDesc = $stkCallback['ResultDesc'] ?? 'Payment failed';

                Log::warning('M-Pesa payment failed', [
                    'checkout_request_id' => $checkoutRequestId,
                    'result_code' => $resultCode,
                    'result_desc' => $resultDesc,
                ]);

                return [
                    'status' => 'failed',
                    'checkout_request_id' => $checkoutRequestId,
                    'result_code' => $resultCode,
                    'result_desc' => $resultDesc,
                ];
            }
        } catch (\Exception $e) {
            Log::error('M-Pesa callback processing failed', [
                'error' => $e->getMessage(),
                'callback_data' => $callbackData,
            ]);

            return [
                'status' => 'error',
                'message' => 'Callback processing failed.',
            ];
        }
    }

    /**
     * Query STK Push status
     */
    public function querySTKStatus(string $checkoutRequestId): array
    {
        $token = $this->getAccessToken();

        if (!$token) {
            return [
                'status' => 'error',
                'message' => 'Failed to get M-Pesa access token.',
            ];
        }

        try {
            $timestamp = Carbon::now()->format('YmdHis');
            $password = base64_encode($this->shortcode . $this->passkey . $timestamp);

            $payload = [
                'BusinessShortCode' => $this->shortcode,
                'Password' => $password,
                'Timestamp' => $timestamp,
                'CheckoutRequestID' => $checkoutRequestId,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/mpesa/stkpushquery/v1/query', $payload);

            $data = $response->json();

            if ($response->successful()) {
                return [
                    'status' => 'success',
                    'data' => $data,
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'STK status query failed.',
                    'data' => $data,
                ];
            }
        } catch (\Exception $e) {
            Log::error('M-Pesa STK status query exception', [
                'error' => $e->getMessage(),
                'checkout_request_id' => $checkoutRequestId,
            ]);

            return [
                'status' => 'error',
                'message' => 'STK status query request failed.',
            ];
        }
    }

    /**
     * Format phone number to M-Pesa format (254XXXXXXXXX)
     */
    public function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove any non-numeric characters
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Handle different formats
        if (strlen($phoneNumber) == 10 && substr($phoneNumber, 0, 1) == '0') {
            // 0712345678 -> 254712345678
            return '254' . substr($phoneNumber, 1);
        } elseif (strlen($phoneNumber) == 9) {
            // 712345678 -> 254712345678
            return '254' . $phoneNumber;
        } elseif (strlen($phoneNumber) == 12 && substr($phoneNumber, 0, 3) == '254') {
            // Already in correct format
            return $phoneNumber;
        }

        return $phoneNumber;
    }

    /**
     * Validate phone number
     */
    public function isValidPhoneNumber(string $phoneNumber): bool
    {
        $formatted = $this->formatPhoneNumber($phoneNumber);
        return preg_match('/^254[0-9]{9}$/', $formatted);
    }
}
