@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">My Certificates</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Certificates</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                @forelse($certificates as $certificate)
                <div class="col-xl-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-4">
                                    <span class="avatar-title rounded-circle bg-light text-primary">
                                        <i class="uil-award font-size-24"></i>
                                    </span>
                                </div>
                                <div class="flex-1">
                                    <h5 class="font-size-15 mb-1">{{ $certificate->course->title }}</h5>
                                    <p class="text-muted mb-2">Certificate #{{ $certificate->certificate_number }}</p>
                                    <p class="text-muted mb-0">Issued: {{ $certificate->issued_at->format('M d, Y') }}</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                @if($certificate->isValid())
                                    <span class="badge badge-soft-success me-2">Valid</span>
                                @else
                                    <span class="badge badge-soft-warning me-2">Expired</span>
                                @endif
                                <div class="float-end">
                                    <button class="btn btn-primary btn-sm">Download</button>
                                    <button class="btn btn-outline-primary btn-sm">Share</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <div class="avatar-md mx-auto mb-4">
                                <div class="avatar-title rounded-circle bg-primary bg-soft text-primary">
                                    <i class="uil-award font-size-24"></i>
                                </div>
                            </div>
                            <h5 class="font-size-16">No Certificates Yet</h5>
                            <p class="text-muted">Complete courses to earn certificates and showcase your achievements.</p>
                            <a href="{{ route('visitors.courses') }}" class="btn btn-primary">Browse Courses</a>
                        </div>
                    </div>
                </div>
                @endforelse
            </div>

            @if($certificates->hasPages())
            <div class="row">
                <div class="col-lg-12">
                    <div class="pagination-wrap hstack gap-2 justify-content-center">
                        {{ $certificates->links() }}
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection