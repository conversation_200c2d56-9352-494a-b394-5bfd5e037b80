<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Zoom API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Zoom API integration including API credentials
    | and webhook settings.
    |
    */

    'account_id' => env('ZOOM_ACCOUNT_ID'),
    'client_id' => env('ZOOM_CLIENT_ID'),
    'client_secret' => env('ZOOM_CLIENT_SECRET'),
    'webhook_secret' => env('ZOOM_WEBHOOK_SECRET'),
    
    // Legacy JWT support (deprecated)
    'api_key' => env('ZOOM_API_KEY'),
    'api_secret' => env('ZOOM_API_SECRET'),
    
    /*
    |--------------------------------------------------------------------------
    | Default Meeting Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for creating Zoom meetings
    |
    */
    
    'default_settings' => [
        'timezone' => 'Africa/Nairobi',
        'duration' => 60, // Default duration in minutes
        'minimum_attendance_minutes' => 45,
        'host_video' => true,
        'participant_video' => true,
        'join_before_host' => false,
        'mute_upon_entry' => true,
        'waiting_room' => true,
        'auto_recording' => 'cloud',
        'approval_type' => 0, // Automatically approve
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Events
    |--------------------------------------------------------------------------
    |
    | List of webhook events to listen for
    |
    */
    
    'webhook_events' => [
        'meeting.participant_joined',
        'meeting.participant_left',
        'meeting.started',
        'meeting.ended',
        'recording.completed',
    ],
];
