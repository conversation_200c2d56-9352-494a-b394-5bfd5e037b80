@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Create New Landing Page</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.landing.index') }}">Landing Pages</a></li>
                                <li class="breadcrumb-item active">Create</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Page Information</h4>

                            <form action="{{ route('admin.landing.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf

                                <div class="row">
                                    <div class="col-md-8">
                                        <!-- Title -->
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Page Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                                   id="title" name="title" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Slug -->
                                        <div class="mb-3">
                                            <label for="slug" class="form-label">Slug</label>
                                            <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                                   id="slug" name="slug" value="{{ old('slug') }}">
                                            <div class="form-text">Leave empty to auto-generate from title</div>
                                            @error('slug')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Content -->
                                        <div class="mb-3">
                                            <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('content') is-invalid @enderror"
                                                      id="content" name="content" rows="15" required>{{ old('content') }}</textarea>
                                            @error('content')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Excerpt -->
                                        <div class="mb-3">
                                            <label for="excerpt" class="form-label">Excerpt</label>
                                            <textarea class="form-control @error('excerpt') is-invalid @enderror"
                                                      id="excerpt" name="excerpt" rows="3">{{ old('excerpt') }}</textarea>
                                            <div class="form-text">Brief description of the page</div>
                                            @error('excerpt')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <!-- Status -->
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                    id="status" name="status" required>
                                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="private" {{ old('status') == 'private' ? 'selected' : '' }}>Private</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Homepage -->
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_homepage"
                                                       name="is_homepage" value="1" {{ old('is_homepage') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_homepage">
                                                    Set as Homepage
                                                </label>
                                            </div>
                                            <div class="form-text">This will replace the current homepage</div>
                                        </div>

                                        <!-- Featured Image -->
                                        <div class="mb-3">
                                            <label for="featured_image" class="form-label">Featured Image</label>
                                            <input type="file" class="form-control @error('featured_image') is-invalid @enderror"
                                                   id="featured_image" name="featured_image" accept="image/*">
                                            <div class="form-text">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</div>
                                            @error('featured_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Published Date -->
                                        <div class="mb-3">
                                            <label for="published_at" class="form-label">Published Date</label>
                                            <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror"
                                                   id="published_at" name="published_at" value="{{ old('published_at') }}">
                                            <div class="form-text">Leave empty for current date/time</div>
                                            @error('published_at')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5 class="mb-3">SEO Settings</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_title" class="form-label">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title') }}" maxlength="255">
                                            <div class="form-text">Recommended: 50-60 characters</div>
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                                      id="meta_description" name="meta_description" rows="3" maxlength="500">{{ old('meta_description') }}</textarea>
                                            <div class="form-text">Recommended: 150-160 characters</div>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="text-end">
                                            <a href="{{ route('admin.landing.index') }}" class="btn btn-secondary me-2">
                                                <i class="mdi mdi-arrow-left me-1"></i> Cancel
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="mdi mdi-content-save me-1"></i> Create Page
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const title = this.value;
    const slug = title.toLowerCase()
        .replace(/[^a-z0-9 -]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});

// Character counter for meta fields
function updateCharCount(inputId, countId, maxLength) {
    const input = document.getElementById(inputId);
    const counter = document.getElementById(countId);
    if (input && counter) {
        input.addEventListener('input', function() {
            const remaining = maxLength - this.value.length;
            counter.textContent = `${this.value.length}/${maxLength} characters`;
            counter.className = remaining < 10 ? 'form-text text-warning' : 'form-text';
        });
    }
}

// Initialize character counters
document.addEventListener('DOMContentLoaded', function() {
    // Add character counters if needed
    const metaTitle = document.getElementById('meta_title');
    const metaDesc = document.getElementById('meta_description');

    if (metaTitle) {
        metaTitle.addEventListener('input', function() {
            const length = this.value.length;
            const feedback = this.parentNode.querySelector('.form-text');
            if (length > 60) {
                feedback.textContent = `${length}/255 characters (recommended: 50-60)`;
                feedback.className = 'form-text text-warning';
            } else {
                feedback.textContent = 'Recommended: 50-60 characters';
                feedback.className = 'form-text';
            }
        });
    }

    if (metaDesc) {
        metaDesc.addEventListener('input', function() {
            const length = this.value.length;
            const feedback = this.parentNode.querySelector('.form-text');
            if (length > 160) {
                feedback.textContent = `${length}/500 characters (recommended: 150-160)`;
                feedback.className = 'form-text text-warning';
            } else {
                feedback.textContent = 'Recommended: 150-160 characters';
                feedback.className = 'form-text';
            }
        });
    }
});
</script>
@endsection
