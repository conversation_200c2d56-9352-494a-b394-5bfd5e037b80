@extends('admin.dashboard')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Add Course</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.courses.index') }}">Courses</a></li>
                                <li class="breadcrumb-item active">Add Course</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <form action="{{ route('admin.courses.store') }}" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                @csrf
                <div class="row">
                    <div class="col-xl-8">
                        <!-- Basic Information -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Course Information</h4>
                                <p class="card-title-desc">Fill in the basic details about the course.</p>

                                @if ($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="title">Course Title</label>
                                            <input type="text" class="form-control" id="title" name="title"
                                                   placeholder="Enter course title" value="{{ old('title') }}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid course title.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label" for="slug">Course Slug</label>
                                            <input type="text" class="form-control" id="slug" name="slug"
                                                   placeholder="course-slug (auto-generated)" value="{{ old('slug') }}">
                                            <div class="form-text">Leave empty to auto-generate from course title</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="status_main">Status <span class="text-danger">*</span></label>
                                            <select class="form-select {{ $errors->has('status') ? 'is-invalid' : '' }}" id="status_main" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="archived" {{ old('status') == 'archived' ? 'selected' : '' }}>Archived</option>
                                            </select>
                                            @if($errors->has('status'))
                                                <div class="invalid-feedback">{{ $errors->first('status') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="short_description">Short Description</label>
                                            <textarea class="form-control" id="short_description" name="short_description" rows="3"
                                                      placeholder="Brief description of the course">{{ old('short_description') }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="description">Full Description</label>
                                            <textarea class="form-control" id="description" name="description" rows="6"
                                                      placeholder="Detailed description of the course">{{ old('description') }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="learning_objectives">Learning Objectives</label>
                                            <textarea class="form-control" id="learning_objectives" name="learning_objectives" rows="4"
                                                      placeholder="What will students learn from this course?">{{ old('learning_objectives') }}</textarea>
                                            <div class="form-text">List the key learning outcomes (one per line)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="requirements">Prerequisites</label>
                                            <textarea class="form-control" id="requirements" name="requirements" rows="3"
                                                      placeholder="What are the requirements for this course?">{{ old('requirements') }}</textarea>
                                            <div class="form-text">List any prerequisites or requirements</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Course Content</h4>
                                <p class="card-title-desc">Manage course structure and content.</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="duration">Duration (hours)</label>
                                            <input type="number" class="form-control" id="duration" name="duration"
                                                   placeholder="0" value="{{ old('duration') }}" min="0" step="0.5">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="difficulty_level">Difficulty Level</label>
                                            <select class="form-select" id="difficulty_level" name="difficulty_level">
                                                <option value="">Select Level</option>
                                                <option value="beginner" {{ old('difficulty_level') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                                <option value="intermediate" {{ old('difficulty_level') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                                <option value="advanced" {{ old('difficulty_level') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="language">Language</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="">Select Language</option>
                                                <option value="english" {{ old('language') == 'english' ? 'selected' : '' }}>English</option>
                                                <option value="spanish" {{ old('language') == 'spanish' ? 'selected' : '' }}>Spanish</option>
                                                <option value="french" {{ old('language') == 'french' ? 'selected' : '' }}>French</option>
                                                <option value="german" {{ old('language') == 'german' ? 'selected' : '' }}>German</option>
                                                <option value="other" {{ old('language') == 'other' ? 'selected' : '' }}>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="certificate">Certificate Available</label>
                                            <select class="form-select" id="certificate" name="certificate">
                                                <option value="0" {{ old('certificate') == '0' ? 'selected' : '' }}>No Certificate</option>
                                                <option value="1" {{ old('certificate') == '1' ? 'selected' : '' }}>Certificate Available</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Session/Meeting Settings -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Live Session Settings</h4>
                                <p class="card-title-desc">Configure Zoom meeting for this course.</p>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="has_live_session" name="has_live_session" value="1"
                                                       {{ old('has_live_session') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="has_live_session">
                                                    <strong>This course has a live session</strong>
                                                </label>
                                                <div class="form-text">Enable this to schedule a Zoom meeting for this course</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="meetingFields" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_topic">Meeting Topic</label>
                                                <input type="text" class="form-control" id="meeting_topic" name="meeting_topic"
                                                       placeholder="Enter meeting topic" value="{{ old('meeting_topic') }}">
                                                <div class="form-text">This will be the title of your Zoom meeting</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_description">Meeting Description</label>
                                                <textarea class="form-control" id="meeting_description" name="meeting_description" rows="3"
                                                          placeholder="Describe what will be covered in the live session">{{ old('meeting_description') }}</textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_start_date">
                                                    Start Date <span class="text-danger">*</span>
                                                </label>
                                                <input type="date" class="form-control" id="meeting_start_date" name="meeting_start_date"
                                                       value="{{ old('meeting_start_date') }}"
                                                       min="{{ date('Y-m-d') }}"
                                                       required>
                                                <div class="form-text">Select the date for the live session</div>
                                                @error('meeting_start_date')
                                                    <div class="text-danger small">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_start_time">
                                                    Start Time <span class="text-danger">*</span>
                                                </label>
                                                <input type="time" class="form-control" id="meeting_start_time" name="meeting_start_time"
                                                       value="{{ old('meeting_start_time') }}"
                                                       required>
                                                <div class="form-text">Select the time for the live session</div>
                                                @error('meeting_start_time')
                                                    <div class="text-danger small">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_duration">Duration (minutes)</label>
                                                <input type="number" class="form-control" id="meeting_duration" name="meeting_duration"
                                                       placeholder="60" value="{{ old('meeting_duration', 60) }}" min="15" max="480">
                                                <div class="form-text">Meeting duration in minutes (15-480)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_password">Meeting Password</label>
                                                <input type="text" class="form-control" id="meeting_password" name="meeting_password"
                                                       placeholder="Optional password" value="{{ old('meeting_password') }}">
                                                <div class="form-text">Leave empty for no password</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="minimum_attendance_minutes">Minimum Attendance (minutes)</label>
                                                <input type="number" class="form-control" id="minimum_attendance_minutes" name="minimum_attendance_minutes"
                                                       placeholder="45" value="{{ old('minimum_attendance_minutes', 45) }}" min="1">
                                                <div class="form-text">Minimum time students must attend to qualify</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check mt-4">
                                                    <input type="checkbox" class="form-check-input" id="meeting_required_for_completion" name="meeting_required_for_completion" value="1"
                                                           {{ old('meeting_required_for_completion') ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="meeting_required_for_completion">
                                                        Required for course completion
                                                    </label>
                                                    <div class="form-text">Students must attend the meeting to complete the course</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="uil-info-circle me-2"></i>
                                        <strong>Note:</strong> The Zoom meeting will be created automatically when you save the course. 
                                        Students will be able to join the meeting 15 minutes before the scheduled start time.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SEO Settings -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">SEO Settings</h4>
                                <p class="card-title-desc">Optimize your course for search engines.</p>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="meta_title">Meta Title</label>
                                            <input type="text" class="form-control" id="meta_title" name="meta_title"
                                                   placeholder="SEO title for this course" value="{{ old('meta_title') }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="meta_description">Meta Description</label>
                                            <textarea class="form-control" id="meta_description" name="meta_description" rows="3"
                                                      placeholder="SEO description for this course">{{ old('meta_description') }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="tags">Tags</label>
                                            <input type="text" class="form-control" id="tags" name="tags"
                                                   placeholder="course, programming, web development" value="{{ old('tags') }}">
                                            <div class="form-text">Separate tags with commas</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4">
                        <!-- Course Settings -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Course Settings</h4>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label class="form-label mb-0" for="category_id">Category</label>
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                            <i class="uil-plus me-1"></i>Add New
                                        </button>
                                    </div>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories ?? [] as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a category.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label class="form-label mb-0" for="instructor_id">Instructor</label>
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addInstructorModal">
                                            <i class="uil-user-plus me-1"></i>Add New
                                        </button>
                                    </div>
                                    <select class="form-select" id="instructor_id" name="instructor_id" required>
                                        <option value="">Select Instructor</option>
                                        @foreach($instructors ?? [] as $instructor)
                                            <option value="{{ $instructor->id }}" {{ old('instructor_id') == $instructor->id ? 'selected' : '' }}>
                                                {{ $instructor->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select an instructor.
                                    </div>
                                </div>



                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" value="1"
                                               {{ old('is_featured') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Featured Course
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_free" name="is_free" value="1"
                                               {{ old('is_free') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_free">
                                            Free Course
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="card" id="pricingCard">
                            <div class="card-body">
                                <h4 class="card-title">Pricing</h4>

                                <div class="mb-3">
                                    <label class="form-label" for="price">Price ($)</label>
                                    <input type="number" class="form-control" id="price" name="price"
                                           placeholder="0.00" value="{{ old('price') }}" min="0" step="0.01">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label" for="discount_price">Discount Price ($)</label>
                                    <input type="number" class="form-control" id="discount_price" name="discount_price"
                                           placeholder="0.00" value="{{ old('discount_price') }}" min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- Course Media -->
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Course Media</h4>

                                <div class="mb-3">
                                    <label class="form-label" for="thumbnail">Course Thumbnail</label>
                                    <input type="file" class="form-control" id="thumbnail" name="thumbnail" accept="image/*">
                                    <div class="form-text">Recommended size: 800x600px</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label" for="preview_video">Preview Video URL</label>
                                    <input type="url" class="form-control" id="preview_video" name="preview_video"
                                           placeholder="https://youtube.com/watch?v=..." value="{{ old('preview_video') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="uil-check me-1"></i> Create Course
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                        <i class="uil-arrow-left me-1"></i> Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>
@endsection

@section('scripts')
<!-- Include Inline Creation JavaScript -->
<script src="{{ asset('js/course-inline-creation.js') }}"></script>

<script>
    // Auto-generate slug from course title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end

        document.getElementById('slug').value = slug;
    });

    // Toggle pricing card based on free course checkbox
    document.getElementById('is_free').addEventListener('change', function() {
        const pricingCard = document.getElementById('pricingCard');
        const priceInput = document.getElementById('price');
        const discountPriceInput = document.getElementById('discount_price');

        if (this.checked) {
            pricingCard.style.opacity = '0.5';
            priceInput.disabled = true;
            discountPriceInput.disabled = true;
            priceInput.value = '0';
            discountPriceInput.value = '0';
        } else {
            pricingCard.style.opacity = '1';
            priceInput.disabled = false;
            discountPriceInput.disabled = false;
        }
    });

    // Validate discount price is less than regular price
    document.getElementById('discount_price').addEventListener('input', function() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const discountPrice = parseFloat(this.value) || 0;

        if (discountPrice > price && price > 0) {
            this.setCustomValidity('Discount price cannot be greater than regular price');
        } else {
            this.setCustomValidity('');
        }
    });

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    // Debug: Log form data before submission
                    const formData = new FormData(form);
                    console.log('Form submission data:');
                    for (let [key, value] of formData.entries()) {
                        console.log(key + ': ' + value);
                    }
                    console.log('Status field value:', document.getElementById('status_main').value);
                    console.log('Featured checkbox checked:', document.getElementById('is_featured').checked);

                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('Form validation failed');
                    } else {
                        console.log('Form validation passed');
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle meeting fields based on live session checkbox
    document.getElementById('has_live_session').addEventListener('change', function() {
        console.log('Live session checkbox changed:', this.checked);
        const meetingFields = document.getElementById('meetingFields');
        const meetingInputs = meetingFields.querySelectorAll('input, textarea, select');
        const meetingStartDate = document.getElementById('meeting_start_date');
        const meetingStartTime = document.getElementById('meeting_start_time');

        if (this.checked) {
            console.log('Showing meeting fields');
            meetingFields.style.display = 'block';
            // Make date and time fields required
            meetingStartDate.setAttribute('required', 'required');
            meetingStartTime.setAttribute('required', 'required');

            // Auto-populate meeting topic from course title if empty
            const courseTitle = document.getElementById('title').value;
            const meetingTopic = document.getElementById('meeting_topic');
            if (courseTitle && !meetingTopic.value) {
                meetingTopic.value = courseTitle + ' - Live Session';
            }
        } else {
            console.log('Hiding meeting fields');
            meetingFields.style.display = 'none';
            // Remove required attributes
            meetingStartDate.removeAttribute('required');
            meetingStartTime.removeAttribute('required');

            // Clear meeting fields when disabled
            meetingInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });
            // Reset default values
            document.getElementById('meeting_duration').value = '60';
            document.getElementById('minimum_attendance_minutes').value = '45';
        }
    });

    // Auto-update meeting topic when course title changes
    document.getElementById('title').addEventListener('input', function() {
        const hasLiveSession = document.getElementById('has_live_session').checked;
        const meetingTopic = document.getElementById('meeting_topic');
        
        if (hasLiveSession && this.value && !meetingTopic.value.includes('Live Session')) {
            meetingTopic.value = this.value + ' - Live Session';
        }
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded - Initializing course form');
        // Trigger free course checkbox change to set initial state
        document.getElementById('is_free').dispatchEvent(new Event('change'));

        // Trigger live session checkbox change to set initial state
        document.getElementById('has_live_session').dispatchEvent(new Event('change'));
        console.log('Course form initialization complete');
    });
</script>

<!-- Include Modal Partials -->
@include('admin.courses.partials.category-modal')
@include('admin.courses.partials.instructor-modal')
@endsection
