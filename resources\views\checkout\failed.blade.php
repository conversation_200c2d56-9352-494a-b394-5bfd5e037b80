@extends('welcome')

@section('content')
<main class="main-area fix">
    <!-- breadcrumb-area -->
    <section class="breadcrumb__area breadcrumb__bg" data-background="{{ asset('frontend/img/bg/breadcrumb_bg.jpg') }}">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="breadcrumb__content">
                        <h3 class="title">Payment Failed</h3>
                        <nav class="breadcrumb">
                            <span property="itemListElement" typeof="ListItem">
                                <a href="{{ url('/') }}">Home</a>
                            </span>
                            <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                            <span property="itemListElement" typeof="ListItem">Payment Failed</span>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <!-- failed-area -->
    <section class="failed__area section-py-120">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="failed__wrapper">
                        <div class="failed__icon">
                            <i class="fas fa-times-circle"></i>
                        </div>

                        <div class="failed__content">
                            <h2 class="failed__title">Payment Failed</h2>
                            <p class="failed__message">
                                We're sorry, but your payment could not be processed at this time.
                                This could be due to various reasons such as insufficient funds,
                                network issues, or payment timeout.
                            </p>

                            <div class="failed__reasons">
                                <h5>Common reasons for payment failure:</h5>
                                <ul class="reasons__list">
                                    <li>
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Insufficient funds in your M-Pesa account
                                    </li>
                                    <li>
                                        <i class="fas fa-clock"></i>
                                        Payment request timed out
                                    </li>
                                    <li>
                                        <i class="fas fa-ban"></i>
                                        Transaction was cancelled by user
                                    </li>
                                    <li>
                                        <i class="fas fa-wifi"></i>
                                        Network connectivity issues
                                    </li>
                                    <li>
                                        <i class="fas fa-lock"></i>
                                        Incorrect M-Pesa PIN entered
                                    </li>
                                </ul>
                            </div>

                            <div class="failed__actions">
                                <a href="{{ route('cart.index') }}" class="btn btn-two">
                                    <i class="fas fa-redo"></i> Try Again
                                </a>
                                <a href="{{ route('visitors.courses') }}" class="btn btn-outline">
                                    <i class="fas fa-book"></i> Browse Courses
                                </a>
                                <a href="{{ url('/') }}" class="btn btn-outline">
                                    <i class="fas fa-home"></i> Back to Home
                                </a>
                            </div>

                            <div class="help__section">
                                <h5>Need Help?</h5>
                                <div class="help__options">
                                    <div class="help__item">
                                        <div class="help__icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="help__content">
                                            <h6>Call Us</h6>
                                            <p>+254 700 123 456</p>
                                            <small>Mon-Fri, 8AM-6PM EAT</small>
                                        </div>
                                    </div>
                                    <div class="help__item">
                                        <div class="help__icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="help__content">
                                            <h6>Email Support</h6>
                                            <p><EMAIL></p>
                                            <small>We'll respond within 24 hours</small>
                                        </div>
                                    </div>
                                    <div class="help__item">
                                        <div class="help__icon">
                                            <i class="fab fa-whatsapp"></i>
                                        </div>
                                        <div class="help__content">
                                            <h6>WhatsApp</h6>
                                            <p>+254 700 123 456</p>
                                            <small>Quick support via WhatsApp</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alternative__payment">
                                <h5>Alternative Payment Methods</h5>
                                <p>If you continue to experience issues with M-Pesa, you can try these alternatives:</p>
                                <div class="payment__alternatives">
                                    <div class="payment__option">
                                        <div class="option__icon">
                                            <i class="fas fa-university"></i>
                                        </div>
                                        <div class="option__content">
                                            <h6>Bank Transfer</h6>
                                            <p>Transfer directly to our bank account and we'll verify your payment manually.</p>
                                            <a href="{{ route('cart.index') }}" class="btn btn-sm btn-outline">Use Bank Transfer</a>
                                        </div>
                                    </div>
                                    <div class="payment__option">
                                        <div class="option__icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="option__content">
                                            <h6>Card Payment</h6>
                                            <p>Pay using your credit or debit card (coming soon).</p>
                                            <button class="btn btn-sm btn-outline" disabled>Coming Soon</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tips__section">
                                <h5>Tips for Successful Payment</h5>
                                <div class="tips__grid">
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Ensure you have sufficient M-Pesa balance</span>
                                    </div>
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Check your network connection</span>
                                    </div>
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Enter the correct M-Pesa PIN</span>
                                    </div>
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Complete payment within 2 minutes</span>
                                    </div>
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Use the phone number registered with M-Pesa</span>
                                    </div>
                                    <div class="tip__item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Ensure your M-Pesa service is active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- failed-area-end -->
</main>

<style>
.failed__wrapper {
    background: #fff;
    padding: 60px 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.failed__icon {
    margin-bottom: 30px;
}

.failed__icon i {
    font-size: 80px;
    color: #dc3545;
    animation: failedShake 1s ease-in-out;
}

@keyframes failedShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.failed__title {
    color: #dc3545;
    margin-bottom: 20px;
    font-size: 32px;
    font-weight: 700;
}

.failed__message {
    font-size: 18px;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.failed__reasons {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 40px;
    text-align: left;
}

.failed__reasons h5 {
    margin-bottom: 20px;
    color: #495057;
    text-align: center;
}

.reasons__list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.reasons__list li {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.reasons__list li:last-child {
    border-bottom: none;
}

.reasons__list li i {
    color: #ffc107;
    font-size: 16px;
    width: 20px;
}

.failed__actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 50px;
}

.failed__actions .btn {
    min-width: 160px;
}

.help__section {
    background: #e3f2fd;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 40px;
    text-align: left;
}

.help__section h5 {
    text-align: center;
    margin-bottom: 30px;
    color: #1976d2;
}

.help__options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.help__item {
    display: flex;
    gap: 15px;
    align-items: flex-start;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
}

.help__icon {
    width: 40px;
    height: 40px;
    background: #1976d2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.help__icon i {
    color: #fff;
    font-size: 16px;
}

.help__content h6 {
    margin-bottom: 5px;
    color: #212529;
}

.help__content p {
    margin-bottom: 3px;
    color: #1976d2;
    font-weight: 600;
}

.help__content small {
    color: #6c757d;
}

.alternative__payment {
    background: #fff3cd;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 40px;
    text-align: left;
    border-left: 4px solid #ffc107;
}

.alternative__payment h5 {
    color: #856404;
    margin-bottom: 15px;
    text-align: center;
}

.alternative__payment > p {
    color: #856404;
    margin-bottom: 25px;
    text-align: center;
}

.payment__alternatives {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.payment__option {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.option__icon {
    width: 50px;
    height: 50px;
    background: #ffc107;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.option__icon i {
    color: #fff;
    font-size: 20px;
}

.option__content h6 {
    margin-bottom: 10px;
    color: #212529;
}

.option__content p {
    margin-bottom: 15px;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

.tips__section {
    text-align: left;
}

.tips__section h5 {
    text-align: center;
    margin-bottom: 30px;
    color: #495057;
}

.tips__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.tip__item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #28a745;
}

.tip__item i {
    color: #28a745;
    font-size: 16px;
    flex-shrink: 0;
}

.tip__item span {
    color: #495057;
    font-size: 14px;
}

@media (max-width: 768px) {
    .failed__wrapper {
        padding: 40px 20px;
    }

    .failed__title {
        font-size: 24px;
    }

    .failed__message {
        font-size: 16px;
    }

    .failed__actions {
        flex-direction: column;
        align-items: center;
    }

    .failed__actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .help__options {
        grid-template-columns: 1fr;
    }

    .payment__alternatives {
        grid-template-columns: 1fr;
    }

    .payment__option {
        flex-direction: column;
        text-align: center;
    }

    .tips__grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endsection
