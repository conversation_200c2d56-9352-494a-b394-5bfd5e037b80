<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class HomepageController extends Controller
{
    public function index()
    {
        $homepageSettings = SiteSetting::where('group', 'homepage')->orderBy('sort_order')->get();
        $headerSettings = SiteSetting::where('group', 'header')->orderBy('sort_order')->get();
        $footerSettings = SiteSetting::where('group', 'footer')->orderBy('sort_order')->get();
        $customSections = SiteSetting::where('group', 'custom_sections')->orderBy('sort_order')->get();
        
        return view('admin.homepage.index', compact('homepageSettings', 'headerSettings', 'footerSettings', 'customSections'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        foreach ($request->settings as $key => $value) {
            // Handle custom section content updates
            if (str_contains($key, '_content')) {
                $sectionKey = str_replace('_content', '', $key);
                $section = SiteSetting::where('key', $sectionKey)->first();
                if ($section) {
                    $sectionData = is_array($section->value) ? $section->value : json_decode($section->value, true);
                    $sectionData['content'] = $value;
                    $section->update(['value' => json_encode($sectionData)]);
                }
                continue;
            }
            
            // Skip enabled flags for custom sections
            if (str_contains($key, '_enabled')) {
                continue;
            }
            
            // Handle file uploads
            if ($request->hasFile("files.{$key}")) {
                $file = $request->file("files.{$key}");
                $path = $file->store('homepage', 'public');
                $value = $path;
            }
            
            SiteSetting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => $this->getGroupFromKey($key),
                    'type' => $this->getFieldType($key)
                ]
            );
        }

        // Clear all caches
        Cache::flush();

        return redirect()->back()->with('success', 'Content updated successfully!');
    }

    public function addCustomSection(Request $request)
    {
        $request->validate([
            'section_name' => 'required|string|max:255',
            'section_type' => 'required|in:text,image,video,testimonial,features,stats'
        ]);

        $sectionKey = 'custom_' . str_replace(' ', '_', strtolower($request->section_name)) . '_' . time();
        
        SiteSetting::create([
            'key' => $sectionKey,
            'value' => json_encode([
                'name' => $request->section_name,
                'type' => $request->section_type,
                'content' => '',
                'enabled' => true
            ]),
            'type' => 'json',
            'group' => 'custom_sections',
            'sort_order' => SiteSetting::where('group', 'custom_sections')->max('sort_order') + 1
        ]);

        return redirect()->back()->with('success', 'Custom section added successfully!');
    }

    public function deleteCustomSection($key)
    {
        SiteSetting::where('key', $key)->delete();
        return redirect()->back()->with('success', 'Custom section deleted successfully!');
    }

    private function getGroupFromKey($key)
    {
        if (str_starts_with($key, 'header_') || in_array($key, ['logo', 'site_name', 'contact_email', 'contact_phone'])) {
            return 'header';
        } elseif (str_starts_with($key, 'footer_') || in_array($key, ['footer_text', 'copyright_text', 'social_facebook', 'social_twitter', 'social_linkedin', 'social_youtube'])) {
            return 'footer';
        } elseif (str_starts_with($key, 'custom_')) {
            return 'custom_sections';
        } else {
            return 'homepage';
        }
    }

    private function getFieldType($key)
    {
        $colorFields = ['primary_color', 'secondary_color', 'accent_color', 'text_color', 'bg_color'];
        $imageFields = ['hero_image', 'about_image', 'newsletter_image', 'instructor_image', 'brand_logo', 'logo', 'footer_logo'];
        $textareaFields = ['hero_description', 'about_description', 'newsletter_description', 'footer_text'];
        $urlFields = ['social_facebook', 'social_twitter', 'social_linkedin', 'social_youtube', 'hero_video_url'];
        $emailFields = ['contact_email'];
        $phoneFields = ['contact_phone'];
        
        if (in_array($key, $colorFields)) {
            return 'color';
        } elseif (in_array($key, $imageFields)) {
            return 'image';
        } elseif (in_array($key, $textareaFields)) {
            return 'textarea';
        } elseif (in_array($key, $urlFields)) {
            return 'url';
        } elseif (in_array($key, $emailFields)) {
            return 'email';
        } elseif (in_array($key, $phoneFields)) {
            return 'tel';
        } elseif (str_contains($key, 'custom_')) {
            return 'json';
        } else {
            return 'text';
        }
    }

    public function seedDefaultSettings()
    {
        $defaultSettings = [
            // Header Settings
            ['key' => 'site_name', 'value' => 'Diagnostic Intelligence', 'type' => 'text', 'group' => 'header', 'sort_order' => 1],
            ['key' => 'logo', 'value' => 'assets/images/logo.png', 'type' => 'image', 'group' => 'header', 'sort_order' => 2],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'email', 'group' => 'header', 'sort_order' => 3],
            ['key' => 'contact_phone', 'value' => '+254 700 000 000', 'type' => 'tel', 'group' => 'header', 'sort_order' => 4],
            
            // Homepage Settings
            ['key' => 'hero_title', 'value' => 'Master Professional Skills with CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 1],
            ['key' => 'hero_highlight_text', 'value' => 'CPD Programs', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 2],
            ['key' => 'hero_description', 'value' => 'Transform your career with industry-recognized CPD courses.', 'type' => 'textarea', 'group' => 'homepage', 'sort_order' => 3],
            ['key' => 'hero_image', 'value' => 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp', 'type' => 'image', 'group' => 'homepage', 'sort_order' => 4],
            ['key' => 'hero_button_text', 'value' => 'Explore Courses', 'type' => 'text', 'group' => 'homepage', 'sort_order' => 5],
            
            // Footer Settings
            ['key' => 'footer_text', 'value' => 'Empowering healthcare professionals with cutting-edge diagnostic education.', 'type' => 'textarea', 'group' => 'footer', 'sort_order' => 1],
            ['key' => 'copyright_text', 'value' => '© 2025 Diagnostic Intelligence. All rights reserved.', 'type' => 'text', 'group' => 'footer', 'sort_order' => 2],
            ['key' => 'social_facebook', 'value' => 'https://www.facebook.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 3],
            ['key' => 'social_linkedin', 'value' => 'https://www.linkedin.com/', 'type' => 'url', 'group' => 'footer', 'sort_order' => 4],
            
            // Colors
            ['key' => 'primary_color', 'value' => '#5751e1', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 100],
            ['key' => 'secondary_color', 'value' => '#3fdacf', 'type' => 'color', 'group' => 'homepage', 'sort_order' => 101],
        ];

        foreach ($defaultSettings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        return redirect()->back()->with('success', 'Default settings seeded successfully!');
    }
}