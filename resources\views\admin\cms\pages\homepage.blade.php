@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Homepage Settings</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.landing.index') }}">Landing Pages</a></li>
                                <li class="breadcrumb-item active">Homepage</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Homepage Configuration</h4>

                            @if(isset($page) && $page->exists)
                            <!-- Current Homepage Info -->
                            <div class="alert alert-info mb-4">
                                <div class="d-flex align-items-center">
                                    <i class="mdi mdi-information-outline me-2"></i>
                                    <div>
                                        <strong>Current Homepage:</strong> {{ $page->title }}
                                        <br>
                                        <small class="text-muted">Last updated: {{ $page->updated_at->format('M d, Y \a\t g:i A') }}</small>
                                    </div>
                                </div>
                            </div>

                            <form action="{{ route('admin.landing.homepage.update') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="page_id" value="{{ $page->id }}">

                                <div class="row">
                                    <div class="col-md-8">
                                        <!-- Title -->
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Homepage Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                                   id="title" name="title" value="{{ old('title', $page->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Content -->
                                        <div class="mb-3">
                                            <label for="content" class="form-label">Homepage Content <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('content') is-invalid @enderror"
                                                      id="content" name="content" rows="20" required>{{ old('content', $page->content) }}</textarea>
                                            @error('content')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Excerpt -->
                                        <div class="mb-3">
                                            <label for="excerpt" class="form-label">Homepage Description</label>
                                            <textarea class="form-control @error('excerpt') is-invalid @enderror"
                                                      id="excerpt" name="excerpt" rows="3">{{ old('excerpt', $page->excerpt) }}</textarea>
                                            <div class="form-text">Brief description for SEO and social sharing</div>
                                            @error('excerpt')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <!-- Status -->
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                    id="status" name="status" required>
                                                <option value="published" {{ old('status', $page->status) == 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="draft" {{ old('status', $page->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Featured Image -->
                                        <div class="mb-3">
                                            <label for="featured_image" class="form-label">Homepage Banner Image</label>
                                            @if($page->featured_image)
                                            <div class="mb-2">
                                                <img src="{{ Storage::url($page->featured_image) }}" alt="Current banner"
                                                     class="img-thumbnail" style="max-width: 200px;">
                                                <div class="form-text">Current banner image</div>
                                            </div>
                                            @endif
                                            <input type="file" class="form-control @error('featured_image') is-invalid @enderror"
                                                   id="featured_image" name="featured_image" accept="image/*">
                                            <div class="form-text">Upload new banner image (optional)</div>
                                            @error('featured_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Quick Actions -->
                                        <div class="mb-3">
                                            <label class="form-label">Quick Actions</label>
                                            <div class="d-grid gap-2">
                                                <a href="{{ url('/') }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="mdi mdi-eye me-1"></i> Preview Homepage
                                                </a>
                                                <a href="{{ route('admin.landing.index') }}" class="btn btn-outline-secondary btn-sm">
                                                    <i class="mdi mdi-file-multiple me-1"></i> Manage All Pages
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5 class="mb-3">SEO Settings</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_title" class="form-label">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title', $page->meta_title) }}" maxlength="255">
                                            <div class="form-text">Recommended: 50-60 characters</div>
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                                      id="meta_description" name="meta_description" rows="3" maxlength="500">{{ old('meta_description', $page->meta_description) }}</textarea>
                                            <div class="form-text">Recommended: 150-160 characters</div>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="text-end">
                                            <a href="{{ route('admin.landing.index') }}" class="btn btn-secondary me-2">
                                                <i class="mdi mdi-arrow-left me-1"></i> Back to Pages
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="mdi mdi-content-save me-1"></i> Update Homepage
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            @else
                            <!-- No Homepage Set -->
                            <div class="text-center py-5">
                                <i class="mdi mdi-home-outline font-size-48 text-muted mb-3"></i>
                                <h5>No Homepage Set</h5>
                                <p class="text-muted mb-4">You haven't set a homepage yet. Create a new page or select an existing page as your homepage.</p>

                                <div class="d-flex justify-content-center gap-2">
                                    <a href="{{ route('admin.landing.create') }}" class="btn btn-primary">
                                        <i class="mdi mdi-plus me-1"></i> Create Homepage
                                    </a>
                                    <a href="{{ route('admin.landing.index') }}" class="btn btn-outline-secondary">
                                        <i class="mdi mdi-file-multiple me-1"></i> Select from Existing Pages
                                    </a>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if(isset($page) && $page->exists)
            <!-- Homepage Statistics -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="mdi mdi-eye font-size-24 text-primary mb-2"></i>
                            <h5 class="mb-1">Page Views</h5>
                            <p class="text-muted mb-0">Coming Soon</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="mdi mdi-clock font-size-24 text-success mb-2"></i>
                            <h5 class="mb-1">Last Updated</h5>
                            <p class="text-muted mb-0">{{ $page->updated_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="mdi mdi-account font-size-24 text-info mb-2"></i>
                            <h5 class="mb-1">Updated By</h5>
                            <p class="text-muted mb-0">{{ $page->updater->name ?? 'System' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
// Character counters for meta fields
document.addEventListener('DOMContentLoaded', function() {
    const metaTitle = document.getElementById('meta_title');
    const metaDesc = document.getElementById('meta_description');

    if (metaTitle) {
        metaTitle.addEventListener('input', function() {
            const length = this.value.length;
            const feedback = this.parentNode.querySelector('.form-text');
            if (length > 60) {
                feedback.textContent = `${length}/255 characters (recommended: 50-60)`;
                feedback.className = 'form-text text-warning';
            } else {
                feedback.textContent = 'Recommended: 50-60 characters';
                feedback.className = 'form-text';
            }
        });
    }

    if (metaDesc) {
        metaDesc.addEventListener('input', function() {
            const length = this.value.length;
            const feedback = this.parentNode.querySelector('.form-text');
            if (length > 160) {
                feedback.textContent = `${length}/500 characters (recommended: 150-160)`;
                feedback.className = 'form-text text-warning';
            } else {
                feedback.textContent = 'Recommended: 150-160 characters';
                feedback.className = 'form-text';
            }
        });
    }
});
</script>
@endsection
