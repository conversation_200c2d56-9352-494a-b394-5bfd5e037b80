<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use App\Services\CartService;

class CartTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create a category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create an instructor
        $instructor = User::create([
            'name' => 'Test Instructor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
        ]);

        // Create test courses
        Course::create([
            'title' => 'Test Course 1',
            'slug' => 'test-course-1',
            'short_description' => 'A test course',
            'description' => 'This is a test course for cart functionality',
            'category_id' => $category->id,
            'instructor_id' => $instructor->id,
            'status' => 'published',
            'duration' => 10.5,
            'is_free' => false,
            'price' => 2500.00,
            'created_by' => $instructor->id,
        ]);

        Course::create([
            'title' => 'Free Test Course',
            'slug' => 'free-test-course',
            'short_description' => 'A free test course',
            'description' => 'This is a free test course',
            'category_id' => $category->id,
            'instructor_id' => $instructor->id,
            'status' => 'published',
            'duration' => 5.0,
            'is_free' => true,
            'price' => 0,
            'created_by' => $instructor->id,
        ]);
    }

    public function test_cart_service_can_add_course()
    {
        $cartService = new CartService();
        $course = Course::where('is_free', false)->first();

        $result = $cartService->add($course, 1);

        $this->assertEquals('success', $result['status']);
        $this->assertEquals(1, $cartService->getItemCount());
        $this->assertTrue($cartService->hasItem($course->id));
    }

    public function test_cart_service_can_remove_course()
    {
        $cartService = new CartService();
        $course = Course::where('is_free', false)->first();

        // Add course first
        $cartService->add($course, 1);
        $this->assertEquals(1, $cartService->getItemCount());

        // Remove course
        $result = $cartService->remove($course->id);

        $this->assertEquals('success', $result['status']);
        $this->assertEquals(0, $cartService->getItemCount());
        $this->assertFalse($cartService->hasItem($course->id));
    }

    public function test_cart_service_calculates_total_correctly()
    {
        $cartService = new CartService();
        $course = Course::where('is_free', false)->first();

        $cartService->add($course, 2);

        $expectedTotal = $course->price * 2;
        $this->assertEquals($expectedTotal, $cartService->getTotal());
        $this->assertEquals('KES ' . number_format($expectedTotal, 2), $cartService->getFormattedTotal());
    }

    public function test_cart_add_endpoint_works()
    {
        $course = Course::where('is_free', false)->first();

        $response = $this->postJson("/add-to-cart/{$course->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'cart_count' => 1,
                 ]);
    }

    public function test_cart_add_endpoint_prevents_duplicate_courses()
    {
        $course = Course::where('is_free', false)->first();

        // Add course first time
        $this->postJson("/add-to-cart/{$course->id}");

        // Try to add same course again
        $response = $this->postJson("/add-to-cart/{$course->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'info',
                 ]);
    }

    public function test_cart_remove_endpoint_works()
    {
        $course = Course::where('is_free', false)->first();

        // Add course first
        $this->postJson("/add-to-cart/{$course->id}");

        // Remove course
        $response = $this->deleteJson("/cart/remove/{$course->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'cart_count' => 0,
                 ]);
    }

    public function test_cart_page_loads()
    {
        $response = $this->get('/cart');

        $response->assertStatus(200)
                 ->assertViewIs('cart.index');
    }

    public function test_cart_count_endpoint_works()
    {
        $response = $this->getJson('/cart/count');

        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'cart_count' => 0,
                 ]);
    }

    public function test_currency_formatting()
    {
        $cartService = new CartService();
        
        $formatted = $cartService->formatCurrency(2500.50);
        $this->assertEquals('KES 2,500.50', $formatted);

        $formatted = $cartService->formatCurrency(0);
        $this->assertEquals('KES 0.00', $formatted);
    }
}
