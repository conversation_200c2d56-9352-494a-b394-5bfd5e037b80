@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">
                            Diagnostic Courses
                            @if($category)
                                - {{ ucwords(str_replace('-', ' ', $category)) }}
                            @endif
                        </h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Diagnostic Courses</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5 class="card-title">Available Courses</h5>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex gap-2 justify-content-md-end">
                                        <a href="{{ route('user.diagnostic.courses') }}" class="btn btn-outline-primary btn-sm {{ !$category ? 'active' : '' }}">All</a>
                                        <a href="{{ route('user.diagnostic.courses', 'medical-imaging') }}" class="btn btn-outline-primary btn-sm {{ $category == 'medical-imaging' ? 'active' : '' }}">Medical Imaging</a>
                                        <a href="{{ route('user.diagnostic.courses', 'laboratory-diagnostics') }}" class="btn btn-outline-primary btn-sm {{ $category == 'laboratory-diagnostics' ? 'active' : '' }}">Lab Diagnostics</a>
                                        <a href="{{ route('user.diagnostic.courses', 'clinical-decision-making') }}" class="btn btn-outline-primary btn-sm {{ $category == 'clinical-decision-making' ? 'active' : '' }}">Clinical Decision</a>
                                        <a href="{{ route('user.diagnostic.courses', 'pathology') }}" class="btn btn-outline-primary btn-sm {{ $category == 'pathology' ? 'active' : '' }}">Pathology</a>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                @forelse($courses as $course)
                                <div class="col-xl-4 col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            @if($course->thumbnail)
                                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="img-fluid rounded mb-3" style="height: 200px; width: 100%; object-fit: cover;">
                                            @else
                                                <div class="bg-primary bg-soft rounded mb-3 d-flex align-items-center justify-content-center" style="height: 200px;">
                                                    <i class="uil-microscope font-size-48 text-primary"></i>
                                                </div>
                                            @endif
                                            
                                            <h5 class="font-size-15 mb-2">{{ $course->title }}</h5>
                                            <p class="text-muted mb-3">{{ Str::limit($course->short_description, 100) }}</p>
                                            
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <span class="badge badge-soft-primary">{{ $course->difficulty_level ?? 'Beginner' }}</span>
                                                    @if($course->duration)
                                                        <span class="badge badge-soft-info">{{ $course->duration }}h</span>
                                                    @endif
                                                </div>
                                                <div class="text-end">
                                                    @if($course->is_free)
                                                        <h6 class="text-success mb-0">Free</h6>
                                                    @else
                                                        <h6 class="mb-0">{{ $course->formatted_price }}</h6>
                                                    @endif
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex gap-2">
                                                <a href="{{ route('course.show', $course->slug) }}" class="btn btn-primary btn-sm flex-fill">View Details</a>
                                                @if(!$course->is_free)
                                                    <button class="btn btn-outline-primary btn-sm" onclick="addToCart({{ $course->id }})">
                                                        <i class="uil-shopping-cart"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @empty
                                <div class="col-12">
                                    <div class="text-center py-5">
                                        <div class="avatar-md mx-auto mb-4">
                                            <div class="avatar-title rounded-circle bg-primary bg-soft text-primary">
                                                <i class="uil-microscope font-size-24"></i>
                                            </div>
                                        </div>
                                        <h5 class="font-size-16">No Courses Found</h5>
                                        <p class="text-muted">No diagnostic courses available in this category.</p>
                                        <a href="{{ route('visitors.courses') }}" class="btn btn-primary">Browse All Courses</a>
                                    </div>
                                </div>
                                @endforelse
                            </div>

                            @if($courses->hasPages())
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="pagination-wrap hstack gap-2 justify-content-center">
                                        {{ $courses->links() }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addToCart(courseId) {
    // Add to cart functionality
    fetch(`/cart/add/${courseId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Course added to cart successfully!');
        } else {
            alert('Error adding course to cart.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding course to cart.');
    });
}
</script>
@endsection