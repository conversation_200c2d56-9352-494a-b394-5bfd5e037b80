@extends('layouts.admin')

@section('title', 'Manage Home Page')

@section('content')
<div class="container">
    <h1>Manage Home Page</h1>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    <form action="{{ route('admin.cms.pages.update', $page) }}" method="POST">
        @csrf
        @method('PUT')

        <div class="form-group">
            <label for="title">Title</label>
            <input type="text" name="title" id="title" class="form-control" value="{{ old('title', $page->title) }}">
        </div>

        <div class="form-group">
            <label for="content">Content</label>
            <textarea name="content" id="content" class="form-control" rows="10">{{ old('content', $page->content) }}</textarea>
        </div>

        <input type="hidden" name="is_homepage" value="1">

        <button type="submit" class="btn btn-primary">Save</button>
    </form>
</div>
@endsection
