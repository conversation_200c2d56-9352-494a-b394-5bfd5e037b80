<?php

namespace App\Http\Controllers;

use App\Models\ZoomMeeting;
use App\Models\Course;
use App\Models\User;
use App\Services\ZoomService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MeetingController extends Controller
{
    protected $zoomService;

    public function __construct(ZoomService $zoomService)
    {
        $this->zoomService = $zoomService;
    }

    /**
     * Display upcoming meetings for the authenticated user
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get meetings for courses the user is enrolled in
        $upcomingMeetings = ZoomMeeting::with(['course', 'attendances' => function($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->whereHas('course.enrollments', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->upcoming()
            ->orderBy('start_time')
            ->get();

        $activeMeetings = ZoomMeeting::with(['course', 'attendances' => function($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->whereHas('course.enrollments', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->active()
            ->get();

        return view('user.meetings.index', compact('upcomingMeetings', 'activeMeetings'));
    }

    /**
     * Show meeting details
     */
    public function show(ZoomMeeting $meeting)
    {
        $user = Auth::user();
        
        // Check if user is enrolled in the course
        $enrollment = $meeting->course->enrollments()->where('user_id', $user->id)->first();
        if (!$enrollment) {
            abort(403, 'You are not enrolled in this course.');
        }

        $attendance = $meeting->getUserAttendance($user);
        
        return view('user.meetings.show', compact('meeting', 'attendance'));
    }

    /**
     * Join a meeting (redirect to Zoom)
     */
    public function join(ZoomMeeting $meeting)
    {
        $user = Auth::user();
        
        // Check if user is enrolled in the course
        $enrollment = $meeting->course->enrollments()->where('user_id', $user->id)->first();
        if (!$enrollment) {
            return redirect()->back()->with('error', 'You are not enrolled in this course.');
        }

        // Check if meeting can be joined
        if (!$meeting->can_join) {
            return redirect()->back()->with('error', 'This meeting is not available for joining at this time.');
        }

        // Additional security: Check if user has already joined from another session
        $existingAttendance = $meeting->attendances()
            ->where('user_id', $user->id)
            ->where('status', 'in_meeting')
            ->first();
            
        if ($existingAttendance) {
            return redirect()->back()->with('error', 'You are already in this meeting from another device/session.');
        }

        // Rate limiting: Prevent rapid join attempts
        $recentJoinAttempts = $meeting->attendances()
            ->where('user_id', $user->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();
            
        if ($recentJoinAttempts >= 3) {
            return redirect()->back()->with('error', 'Too many join attempts. Please wait a few minutes before trying again.');
        }

        // Log the join attempt with additional security info
        Log::info('User joining meeting', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'meeting_id' => $meeting->id,
            'zoom_meeting_id' => $meeting->zoom_meeting_id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'enrollment_id' => $enrollment->id
        ]);

        // Create attendance record to track join attempt
        $meeting->attendances()->create([
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'joined_at' => now(),
            'status' => 'joined',
            'zoom_data' => [
                'join_method' => 'web_redirect',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]
        ]);

        // Generate a unique join URL with user-specific parameters
        $joinUrl = $meeting->join_url . '?uname=' . urlencode($user->name) . '&uid=' . $user->id;

        // Redirect to Zoom meeting
        return redirect($joinUrl);
    }

    /**
     * Handle Zoom webhooks
     */
    public function webhook(Request $request)
    {
        // Verify webhook signature if configured
        if (config('services.zoom.webhook_secret')) {
            $signature = $request->header('x-zm-signature');
            $timestamp = $request->header('x-zm-request-timestamp');
            $body = $request->getContent();
            
            $expectedSignature = hash_hmac('sha256', 
                'v0:' . $timestamp . ':' . $body, 
                config('services.zoom.webhook_secret')
            );
            
            if (!hash_equals('v0=' . $expectedSignature, $signature)) {
                Log::warning('Invalid webhook signature');
                return response()->json(['error' => 'Invalid signature'], 401);
            }
        }

        try {
            $this->zoomService->processWebhook($request->all());
            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(['error' => 'Processing failed'], 500);
        }
    }

    /**
     * Admin: List all meetings
     */
    public function adminIndex()
    {
        $meetings = ZoomMeeting::with(['course', 'creator'])
            ->orderBy('start_time', 'desc')
            ->paginate(20);

        return view('admin.meetings.index', compact('meetings'));
    }

    /**
     * Admin: Show meeting details with attendance
     */
    public function adminShow(ZoomMeeting $meeting)
    {
        $meeting->load(['course', 'creator', 'attendances.user']);
        
        $stats = [
            'total_enrolled' => $meeting->course->enrollments()->count(),
            'total_attendees' => $meeting->getTotalAttendees(),
            'qualified_attendees' => $meeting->getQualifiedAttendees(),
            'attendance_rate' => $meeting->course->enrollments()->count() > 0 
                ? round(($meeting->getTotalAttendees() / $meeting->course->enrollments()->count()) * 100, 2)
                : 0
        ];

        return view('admin.meetings.show', compact('meeting', 'stats'));
    }

    /**
     * Admin: Sync meeting attendance from Zoom
     */
    public function syncAttendance(ZoomMeeting $meeting)
    {
        try {
            $this->zoomService->syncMeetingAttendance($meeting);
            return redirect()->back()->with('success', 'Attendance synced successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to sync attendance: ' . $e->getMessage());
        }
    }

    /**
     * Admin: Update meeting status
     */
    public function updateStatus(ZoomMeeting $meeting)
    {
        $meeting->updateStatus();
        return redirect()->back()->with('success', 'Meeting status updated!');
    }

    /**
     * Get meeting recordings for enrolled users
     */
    public function recordings(ZoomMeeting $meeting)
    {
        $user = Auth::user();
        
        // Check if user is enrolled in the course
        $enrollment = $meeting->course->enrollments()->where('user_id', $user->id)->first();
        if (!$enrollment) {
            abort(403, 'You are not enrolled in this course.');
        }

        // Check if recording is available
        if (!$meeting->recording_available || !$meeting->recording_url) {
            return redirect()->back()->with('error', 'Recording is not yet available for this meeting.');
        }

        // For security, you might want to generate a temporary signed URL
        // or proxy the recording through your application
        return redirect($meeting->recording_url);
    }

    /**
     * Admin: Manually update recording URL
     */
    public function updateRecording(Request $request, ZoomMeeting $meeting)
    {
        $request->validate([
            'recording_url' => 'required|url'
        ]);

        $meeting->update([
            'recording_url' => $request->recording_url,
            'recording_available' => true
        ]);

        return redirect()->back()->with('success', 'Recording URL updated successfully!');
    }
}
