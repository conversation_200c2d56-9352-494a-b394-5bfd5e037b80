@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Landing Page Management</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Landing Pages</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <h4 class="card-title">All Landing Pages</h4>
                                </div>
                                <div class="col-sm-6">
                                    <div class="text-sm-end">
                                        <a href="{{ route('admin.landing.create') }}" class="btn btn-success btn-rounded waves-effect waves-light mb-2 me-2">
                                            <i class="mdi mdi-plus me-1"></i> Add New Page
                                        </a>
                                        <a href="{{ route('admin.landing.homepage') }}" class="btn btn-primary btn-rounded waves-effect waves-light mb-2">
                                            <i class="mdi mdi-home me-1"></i> Homepage Settings
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <form method="GET" action="{{ route('admin.landing.index') }}">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="search"
                                                   placeholder="Search pages..." value="{{ request('search') }}">
                                            <button class="btn btn-outline-secondary" type="submit">
                                                <i class="mdi mdi-magnify"></i>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <form method="GET" action="{{ route('admin.landing.index') }}">
                                        <select name="status" class="form-select" onchange="this.form.submit()">
                                            <option value="">All Status</option>
                                            <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="private" {{ request('status') == 'private' ? 'selected' : '' }}>Private</option>
                                        </select>
                                    </form>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-centered table-nowrap mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Title</th>
                                            <th>Slug</th>
                                            <th>Status</th>
                                            <th>Homepage</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($pages ?? [] as $page)
                                        <tr>
                                            <td>
                                                <h6 class="mb-0">{{ $page->title }}</h6>
                                                @if($page->excerpt)
                                                    <p class="text-muted mb-0 font-size-12">{{ Str::limit($page->excerpt, 50) }}</p>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info-subtle text-info">{{ $page->slug }}</span>
                                            </td>
                                            <td>
                                                @if($page->status == 'published')
                                                    <span class="badge bg-success-subtle text-success">Published</span>
                                                @elseif($page->status == 'draft')
                                                    <span class="badge bg-warning-subtle text-warning">Draft</span>
                                                @else
                                                    <span class="badge bg-secondary-subtle text-secondary">Private</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($page->is_homepage)
                                                    <span class="badge bg-primary-subtle text-primary">
                                                        <i class="mdi mdi-home"></i> Homepage
                                                    </span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ $page->created_at->format('M d, Y') }}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.landing.edit', $page) }}"
                                                       class="btn btn-outline-secondary btn-sm">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                                            onclick="deletePage({{ $page->id }})">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="mdi mdi-file-document-outline font-size-48 mb-3"></i>
                                                    <h5>No pages found</h5>
                                                    <p>Create your first landing page to get started.</p>
                                                    <a href="{{ route('admin.landing.create') }}" class="btn btn-primary">
                                                        <i class="mdi mdi-plus me-1"></i> Create Page
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            @if(isset($pages) && $pages->hasPages())
                            <div class="row mt-3">
                                <div class="col-sm-6">
                                    <div class="dataTables_info">
                                        Showing {{ $pages->firstItem() }} to {{ $pages->lastItem() }} of {{ $pages->total() }} entries
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="dataTables_paginate paging_simple_numbers float-end">
                                        {{ $pages->links() }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this page? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deletePage(pageId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/landing-page/${pageId}/destroy`;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection
