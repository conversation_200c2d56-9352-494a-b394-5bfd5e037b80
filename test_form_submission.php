<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Admin\CourseController;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use App\Models\ZoomMeeting;

echo "=== Testing Form Submission Process ===\n\n";

// Clear logs for this test
Log::info('=== Starting form submission test ===');

// 1. Setup prerequisites
$category = Category::first();
$user = User::first();

if (!$category || !$user) {
    echo "✗ Missing prerequisites (category or user)\n";
    exit(1);
}

// Authenticate user
Auth::login($user);
echo "✓ User authenticated: {$user->name}\n";

// 2. Prepare form data exactly as it would come from the browser
$futureDate = date('Y-m-d', strtotime('+7 days'));
$futureTime = '15:30';

$formData = [
    '_token' => csrf_token(),
    'title' => 'Browser Test Course - ' . date('Y-m-d H:i:s'),
    'short_description' => 'Test course from browser simulation',
    'description' => 'Detailed description for browser test course',
    'category_id' => $category->id,
    'instructor_id' => $user->id,
    'status' => 'draft',
    'duration' => '3.0',
    'difficulty_level' => 'beginner',
    'language' => 'English',
    'is_free' => '1',
    'has_live_session' => '1',  // This is the key field
    'meeting_topic' => 'Browser Test Live Session',
    'meeting_description' => 'Test meeting from browser simulation',
    'meeting_start_date' => $futureDate,
    'meeting_start_time' => $futureTime,
    'meeting_duration' => '90',
    'minimum_attendance_minutes' => '60',
    'meeting_required_for_completion' => '1',
];

echo "✓ Form data prepared\n";
echo "Meeting scheduled for: {$futureDate} at {$futureTime}\n\n";

// 3. Create a proper Request object
$request = Request::create('/admin-courses-store', 'POST', $formData);
$request->headers->set('Content-Type', 'application/x-www-form-urlencoded');

echo "3. Testing CourseController store method:\n";

try {
    // Get initial counts
    $initialCourses = Course::count();
    $initialMeetings = ZoomMeeting::count();
    
    echo "Initial state - Courses: {$initialCourses}, Meetings: {$initialMeetings}\n";
    
    // Create controller instance
    $controller = new CourseController(new \App\Services\ZoomService());
    
    // Call the store method
    echo "Calling store method...\n";
    $response = $controller->store($request);
    
    // Check the response
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        $targetUrl = $response->getTargetUrl();
        echo "✓ Redirect response received to: {$targetUrl}\n";
        
        // Check session messages
        $session = $response->getSession();
        if ($session && $session->has('success')) {
            echo "✓ Success message: " . $session->get('success') . "\n";
        }
        if ($session && $session->has('error')) {
            echo "✗ Error message: " . $session->get('error') . "\n";
        }
    } else {
        echo "✗ Unexpected response type: " . get_class($response) . "\n";
    }
    
    // Check final counts
    $finalCourses = Course::count();
    $finalMeetings = ZoomMeeting::count();
    
    echo "\nFinal state - Courses: {$finalCourses}, Meetings: {$finalMeetings}\n";
    echo "Courses created: " . ($finalCourses - $initialCourses) . "\n";
    echo "Meetings created: " . ($finalMeetings - $initialMeetings) . "\n";
    
    // Check the latest course
    if ($finalCourses > $initialCourses) {
        $latestCourse = Course::latest()->first();
        echo "\nLatest course:\n";
        echo "- ID: {$latestCourse->id}\n";
        echo "- Title: {$latestCourse->title}\n";
        echo "- Has live session: " . ($latestCourse->has_live_session ? 'Yes' : 'No') . "\n";
        
        $meeting = $latestCourse->zoomMeetings()->first();
        if ($meeting) {
            echo "- Zoom meeting: {$meeting->topic} (ID: {$meeting->zoom_meeting_id})\n";
        } else {
            echo "- Zoom meeting: None\n";
        }
    }
    
} catch (\Exception $e) {
    echo "✗ Exception caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Form Submission Test Complete ===\n";
