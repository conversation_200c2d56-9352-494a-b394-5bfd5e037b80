<?php

namespace App\Services;

use App\Models\ZoomMeeting;
use App\Models\MeetingAttendance;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ZoomService
{
    private $accountId;
    private $clientId;
    private $clientSecret;
    private $baseUrl;

    public function __construct()
    {
        $this->accountId = config('services.zoom.account_id');
        $this->clientId = config('services.zoom.client_id');
        $this->clientSecret = config('services.zoom.client_secret');
        $this->baseUrl = 'https://api.zoom.us/v2';
    }

    /**
     * Get OAuth 2.0 access token for Zoom API authentication
     */
    private function getAccessToken(): string
    {
        $cacheKey = 'zoom_access_token';

        // Check if token exists in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
                ->asForm()
                ->post('https://zoom.us/oauth/token', [
                    'grant_type' => 'account_credentials',
                    'account_id' => $this->accountId,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['access_token'];
                $expiresIn = $data['expires_in'] ?? 3600;

                // Cache token for slightly less than expiry time
                Cache::put($cacheKey, $token, now()->addSeconds($expiresIn - 300));

                Log::info('Zoom OAuth token obtained successfully');
                return $token;
            } else {
                Log::error('Zoom OAuth token request failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                throw new \Exception('Failed to get Zoom access token: ' . $response->body());
            }
        } catch (\Exception $e) {
            Log::error('Zoom OAuth token request exception', [
                'error' => $e->getMessage(),
                'account_id' => $this->accountId,
                'client_id' => $this->clientId
            ]);
            throw $e;
        }
    }

    /**
     * Make HTTP request to Zoom API
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $token = $this->getAccessToken();

            Log::info('Making Zoom API request', [
                'method' => $method,
                'endpoint' => $endpoint,
                'data' => $data
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->$method($this->baseUrl . $endpoint, $data);

            if (!$response->successful()) {
                Log::error('Zoom API Error', [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'request_data' => $data
                ]);

                throw new \Exception('Zoom API request failed: ' . $response->body());
            }

            $responseData = $response->json();
            Log::info('Zoom API request successful', [
                'endpoint' => $endpoint,
                'response_keys' => array_keys($responseData)
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error('Zoom API request exception', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Create a new Zoom meeting
     */
    public function createMeeting(array $meetingData): array
    {
        $payload = [
            'topic' => $meetingData['topic'],
            'type' => 2, // Scheduled meeting
            'start_time' => Carbon::parse($meetingData['start_time'])->toISOString(),
            'duration' => $meetingData['duration'],
            'timezone' => $meetingData['timezone'] ?? 'Africa/Nairobi',
            'password' => $meetingData['password'] ?? null,
            'agenda' => $meetingData['description'] ?? '',
            'settings' => [
                'host_video' => true,
                'participant_video' => true,
                'cn_meeting' => false,
                'in_meeting' => false,
                'join_before_host' => false,
                'mute_upon_entry' => true,
                'watermark' => false,
                'use_pmi' => false,
                'approval_type' => 0, // Automatically approve
                'audio' => 'both',
                'auto_recording' => 'cloud', // Enable cloud recording
                'enforce_login' => false,
                'registrants_email_notification' => true,
                'waiting_room' => true,
                'allow_multiple_devices' => true,
            ]
        ];

        return $this->makeRequest('post', '/users/me/meetings', $payload);
    }

    /**
     * Update an existing Zoom meeting
     */
    public function updateMeeting(string $meetingId, array $meetingData): array
    {
        $payload = [
            'topic' => $meetingData['topic'],
            'start_time' => Carbon::parse($meetingData['start_time'])->toISOString(),
            'duration' => $meetingData['duration'],
            'timezone' => $meetingData['timezone'] ?? 'Africa/Nairobi',
            'password' => $meetingData['password'] ?? null,
            'agenda' => $meetingData['description'] ?? '',
        ];

        return $this->makeRequest('patch', "/meetings/{$meetingId}", $payload);
    }

    /**
     * Delete a Zoom meeting
     */
    public function deleteMeeting(string $meetingId): bool
    {
        try {
            $this->makeRequest('delete', "/meetings/{$meetingId}");
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete Zoom meeting', [
                'meeting_id' => $meetingId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get meeting details
     */
    public function getMeeting(string $meetingId): array
    {
        return $this->makeRequest('get', "/meetings/{$meetingId}");
    }

    /**
     * Get meeting participants
     */
    public function getMeetingParticipants(string $meetingId): array
    {
        return $this->makeRequest('get', "/metrics/meetings/{$meetingId}/participants");
    }

    /**
     * Get meeting recordings
     */
    public function getMeetingRecordings(string $meetingId): array
    {
        return $this->makeRequest('get', "/meetings/{$meetingId}/recordings");
    }

    /**
     * Process webhook data for participant events
     */
    public function processWebhook(array $webhookData): void
    {
        $event = $webhookData['event'] ?? null;

        if (!$event) {
            return;
        }

        switch ($event) {
            case 'meeting.participant_joined':
                $this->handleParticipantJoined($webhookData);
                break;
            case 'meeting.participant_left':
                $this->handleParticipantLeft($webhookData);
                break;
            case 'meeting.ended':
                $this->handleMeetingEnded($webhookData);
                break;
            case 'recording.completed':
                $this->handleRecordingCompleted($webhookData);
                break;
        }
    }

    /**
     * Handle participant joined event
     */
    private function handleParticipantJoined(array $data): void
    {
        $payload = $data['payload'] ?? [];
        $meetingId = $payload['object']['id'] ?? null;
        $participant = $payload['object']['participant'] ?? [];

        if (!$meetingId || !$participant) {
            return;
        }

        $meeting = ZoomMeeting::where('zoom_meeting_id', $meetingId)->first();
        if (!$meeting) {
            return;
        }

        // Try to find user by email
        $user = User::where('email', $participant['email'])->first();
        if (!$user) {
            return;
        }

        MeetingAttendance::recordAttendance($meeting, $user, [
            'participant_uuid' => $participant['participant_uuid'],
            'participant_user_id' => $participant['user_id'] ?? null,
            'name' => $participant['user_name'],
            'email' => $participant['email'],
            'joined_at' => $participant['join_time'],
            'status' => 'in_meeting',
        ]);
    }

    /**
     * Handle participant left event
     */
    private function handleParticipantLeft(array $data): void
    {
        $payload = $data['payload'] ?? [];
        $meetingId = $payload['object']['id'] ?? null;
        $participant = $payload['object']['participant'] ?? [];

        if (!$meetingId || !$participant) {
            return;
        }

        $meeting = ZoomMeeting::where('zoom_meeting_id', $meetingId)->first();
        if (!$meeting) {
            return;
        }

        $user = User::where('email', $participant['email'])->first();
        if (!$user) {
            return;
        }

        $attendance = MeetingAttendance::where([
            'zoom_meeting_id' => $meeting->id,
            'user_id' => $user->id,
            'participant_uuid' => $participant['participant_uuid']
        ])->first();

        if ($attendance) {
            $attendance->left_at = Carbon::parse($participant['leave_time']);
            $attendance->status = 'left';
            $attendance->calculateDuration();
            $attendance->save();
        }
    }

    /**
     * Handle meeting ended event
     */
    private function handleMeetingEnded(array $data): void
    {
        $payload = $data['payload'] ?? [];
        $meetingId = $payload['object']['id'] ?? null;

        if (!$meetingId) {
            return;
        }

        $meeting = ZoomMeeting::where('zoom_meeting_id', $meetingId)->first();
        if ($meeting) {
            $meeting->status = 'ended';
            $meeting->save();
        }
    }

    /**
     * Handle recording completed event
     */
    private function handleRecordingCompleted(array $data): void
    {
        $payload = $data['payload'] ?? [];
        $meetingId = $payload['object']['id'] ?? null;
        $recordingFiles = $payload['object']['recording_files'] ?? [];

        if (!$meetingId || empty($recordingFiles)) {
            return;
        }

        $meeting = ZoomMeeting::where('zoom_meeting_id', $meetingId)->first();
        if (!$meeting) {
            return;
        }

        // Find the main recording file (usually MP4)
        $mainRecording = collect($recordingFiles)->first(function ($file) {
            return $file['file_type'] === 'MP4' && $file['recording_type'] === 'shared_screen_with_speaker_view';
        });

        if ($mainRecording) {
            $meeting->recording_url = $mainRecording['play_url'];
            $meeting->recording_available = true;
            $meeting->save();
        }
    }

    /**
     * Sync meeting attendance from Zoom API
     */
    public function syncMeetingAttendance(ZoomMeeting $meeting): void
    {
        try {
            $participants = $this->getMeetingParticipants($meeting->zoom_meeting_id);

            foreach ($participants['participants'] ?? [] as $participant) {
                $user = User::where('email', $participant['user_email'])->first();
                if (!$user) {
                    continue;
                }

                $duration = $participant['duration'] ?? 0;
                $joinTime = isset($participant['join_time']) ? Carbon::parse($participant['join_time']) : null;
                $leaveTime = isset($participant['leave_time']) ? Carbon::parse($participant['leave_time']) : null;

                MeetingAttendance::updateOrCreate([
                    'zoom_meeting_id' => $meeting->id,
                    'user_id' => $user->id,
                ], [
                    'name' => $participant['name'],
                    'email' => $participant['user_email'],
                    'joined_at' => $joinTime,
                    'left_at' => $leaveTime,
                    'duration_minutes' => $duration,
                    'attended_full_session' => $duration >= $meeting->minimum_attendance_minutes,
                    'status' => 'left',
                    'zoom_data' => $participant,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to sync meeting attendance', [
                'meeting_id' => $meeting->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
