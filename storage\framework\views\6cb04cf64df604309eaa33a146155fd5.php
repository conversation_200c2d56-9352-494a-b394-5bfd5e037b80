<style>
    .checkout__wrapper {
        background: #fff;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .checkout__title {
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
    }

    .checkout__input-item {
        margin-bottom: 25px;
    }

    .checkout__input-item label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }

    .checkout__input-item input {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .checkout__input-item input:focus {
        outline: none;
        border-color: var(--tg-theme-primary);
    }

    .checkout__input-item input.is-invalid {
        border-color: #dc3545;
    }

    .checkout__payment {
        margin: 40px 0;
    }

    .checkout__payment-item {
        margin-bottom: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        overflow: hidden;
        transition: border-color 0.3s;
    }

    .checkout__payment-item:has(input:checked) {
        border-color: var(--tg-theme-primary);
    }

    .checkout__payment-label {
        display: block;
        padding: 20px;
        cursor: pointer;
        margin: 0;
        position: relative;
        transition: background-color 0.3s;
    }

    .checkout__payment-label:hover {
        background-color: #f8f9fa;
    }

    .checkout__payment-label.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .checkout__payment-label input[type="radio"] {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .payment-logo {
        height: 30px;
        margin-right: 10px;
        vertical-align: middle;
    }

    .payment-desc {
        display: block;
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }

    .payment-details {
        padding: 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #e0e0e0;
    }

    .bank-info {
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid var(--tg-theme-primary);
    }

    .checkout__order-summary {
        margin: 40px 0 30px 0;
        padding: 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 2px solid #e0e0e0;
    }

    .order-summary-box {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 16px;
    }

    .summary-row:last-child {
        border-bottom: none;
    }

    .summary-row.total-row {
        border-top: 2px solid var(--tg-theme-primary);
        margin-top: 10px;
        padding-top: 15px;
        font-weight: 700;
        font-size: 20px;
        color: var(--tg-theme-primary);
    }

    .summary-row .amount {
        font-weight: 600;
        color: #333;
    }

    .summary-row .total-amount {
        font-weight: 700;
        font-size: 24px;
        color: var(--tg-theme-primary);
    }

    .checkout__agree {
        margin: 30px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .checkout__agree-item {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .checkout__agree-item input[type="checkbox"] {
        margin-top: 3px;
    }

    .checkout__sidebar {
        background: #fff;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 20px;
        border: 1px solid #f0f0f0;
    }

    .checkout__sidebar .title {
        color: var(--tg-theme-primary);
        font-size: 20px;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
    }

    .checkout__order-item {
        display: flex;
        gap: 15px;
        padding: 20px 0;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .checkout__order-item:hover {
        background-color: #f8f9fa;
        margin: 0 -15px;
        padding: 20px 15px;
        border-radius: 8px;
    }

    .checkout__order-item:last-child {
        border-bottom: none;
    }

    .item-thumb img {
        width: 70px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .item-content .title {
        font-size: 15px;
        margin: 0 0 8px 0;
        line-height: 1.4;
        font-weight: 600;
        color: #333;
    }

    .item-content .instructor {
        font-size: 13px;
        color: #666;
        display: block;
        margin-bottom: 10px;
        font-style: italic;
    }

    .item-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
    }

    .item-price .price {
        font-weight: 700;
        color: var(--tg-theme-primary);
        font-size: 16px;
    }

    .item-price .free-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .item-price .qty {
        font-size: 12px;
        color: #666;
        background: #f8f9fa;
        padding: 2px 8px;
        border-radius: 12px;
    }

    .checkout__total-list {
        list-style: none;
        padding: 0;
        margin: 25px 0;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }

    .checkout__total-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
        font-size: 14px;
    }

    .checkout__total-list li:last-child {
        border-bottom: none;
    }

    .checkout__total-list li.subtotal {
        font-weight: 600;
        color: #495057;
        border-top: 2px solid #dee2e6;
        margin-top: 10px;
        padding-top: 15px;
    }

    .checkout__total-list li.total {
        font-weight: 700;
        font-size: 20px;
        color: var(--tg-theme-primary);
        background: white;
        margin: 15px -20px -20px -20px;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        border-top: 2px solid var(--tg-theme-primary);
    }

    .checkout__security {
        display: flex;
        justify-content: space-around;
        margin-top: 25px;
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
    }

    .security-item {
        text-align: center;
        font-size: 12px;
        color: #666;
        flex: 1;
    }

    .security-item i {
        display: block;
        font-size: 24px;
        margin-bottom: 8px;
        color: var(--tg-theme-primary);
    }

    .checkout__promo {
        margin-top: 25px;
        padding: 20px;
        background: #fff3cd;
        border-radius: 10px;
        border-left: 4px solid #ffc107;
    }

    .checkout__promo h6 {
        color: #856404;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .checkout__promo .input-group {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    .checkout__promo .form-control {
        border: none;
        padding: 12px 15px;
    }

    .checkout__promo .btn {
        border: none;
        padding: 12px 20px;
        font-weight: 600;
    }

    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }

    .empty-cart i {
        opacity: 0.5;
    }

    .badge {
        font-size: 12px;
        padding: 6px 12px;
    }

    .required {
        color: #dc3545;
    }

    .form-text {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }

    .invalid-feedback {
        display: none;
        font-size: 12px;
        color: #dc3545;
        margin-top: 5px;
    }

    .is-invalid~.invalid-feedback {
        display: block;
    }

    .payment-timer {
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 6px;
        font-weight: 600;
    }

    #countdown {
        color: var(--tg-theme-primary);
    }
</style>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/checkout/style.blade.php ENDPATH**/ ?>