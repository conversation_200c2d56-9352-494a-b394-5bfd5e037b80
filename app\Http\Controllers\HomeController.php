<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page with courses
     */
    public function index()
    {
        // Check if we have real data in the database
        $hasRealData = Course::count() > 0;

        if ($hasRealData) {
            // Use real data from database
            $featuredCourses = Course::with(['category', 'instructor'])
                ->published()
                ->featured()
                ->orderBy('created_at', 'desc')
                ->limit(8)
                ->get();

            $courses = Course::with(['category', 'instructor'])
                ->published()
                ->orderBy('created_at', 'desc')
                ->limit(12)
                ->get();

            $categories = Category::active()
                ->whereHas('courses', function($query) {
                    $query->published();
                })
                ->with(['courses' => function($query) {
                    $query->published()
                          ->with(['category', 'instructor'])
                          ->orderBy('created_at', 'desc')
                          ->limit(8);
                }])
                ->limit(5)
                ->get();
        } else {
            // Use static demo data for Kenyan Laboratory CPD Provider
            $allCourses = $this->getStaticCourseData();
            $allCategories = $this->getStaticCategoryData();

            // Convert courses to objects and filter featured courses
            $featuredCourses = collect(array_filter($allCourses, fn($course) => $course['is_featured'] && $course['status'] === 'published'))
                ->map(fn($course) => (object) $course);

            // Get published courses as objects
            $courses = collect(array_filter($allCourses, fn($course) => $course['status'] === 'published'))
                ->map(fn($course) => (object) $course);

            // Convert categories to collection and add courses
            $categories = collect($allCategories)->map(function($category) use ($allCourses) {
                $categoryCourses = array_filter($allCourses, fn($course) =>
                    $course['category']->id === $category->id && $course['status'] === 'published'
                );
                $category->courses = collect($categoryCourses)->map(fn($course) => (object) $course);
                return $category;
            });
        }

        return view('index-dynamic', compact('featuredCourses', 'courses', 'categories'));
    }

    /**
     * Display a specific course
     */
    public function show($slug)
    {
        $course = Course::with(['category', 'instructor'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Get related courses from the same category
        $relatedCourses = Course::with(['category', 'instructor'])
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->published()
            ->limit(4)
            ->get();

        return view('course-detail', compact('course', 'relatedCourses'));
    }

    /**
     * Get static course data for Kenyan Laboratory CPD Provider
     */
    private function getStaticCourseData()
    {
        return [
            [
                'id' => 1,
                'title' => 'Clinical Chemistry Fundamentals for KMLTTB Certification',
                'slug' => 'clinical-chemistry-fundamentals-kmlttb',
                'short_description' => 'Comprehensive online course covering clinical chemistry principles, quality control, and laboratory safety standards as required by KMLTTB.',
                'description' => 'This course provides essential knowledge in clinical chemistry for medical laboratory professionals seeking KMLTTB certification. Topics include biochemical analysis, enzyme studies, lipid profiles, liver function tests, kidney function assessment, and quality assurance protocols. Designed specifically for Kenyan laboratory technicians and technologists.',
                'learning_objectives' => "• Master clinical chemistry analytical techniques\n• Understand quality control procedures\n• Apply KMLTTB safety standards\n• Interpret biochemical test results\n• Implement laboratory best practices",
                'requirements' => 'Basic laboratory experience, Certificate/Diploma in Medical Laboratory Technology, Internet connection for online learning',
                'category' => (object)['id' => 1, 'name' => 'Clinical Chemistry', 'slug' => 'clinical-chemistry'],
                'instructor' => (object)['id' => 1, 'name' => 'Dr. Mary Wanjiku, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 40.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => false,
                'price' => 8500.00,
                'discount_price' => 6800.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Clinical Chemistry KMLTTB Certification Course',
                'meta_description' => 'Professional clinical chemistry course for KMLTTB certification. Learn biochemical analysis, quality control, and laboratory safety.',
                'tags' => 'clinical chemistry,KMLTTB,biochemistry,laboratory,certification,medical technology',
                'created_at' => '2024-01-15 08:00:00',
                'updated_at' => '2024-01-15 08:00:00',
            ],
            [
                'id' => 2,
                'title' => 'Hematology and Blood Banking Essentials',
                'slug' => 'hematology-blood-banking-essentials',
                'short_description' => 'Master hematological testing, blood grouping, cross-matching, and transfusion medicine according to KMLTTB standards.',
                'description' => 'Comprehensive training in hematology and blood banking for medical laboratory professionals. This course covers complete blood count interpretation, blood film examination, coagulation studies, blood grouping systems, compatibility testing, and transfusion reactions. Aligned with KMLTTB competency requirements.',
                'learning_objectives' => "• Perform accurate hematological analyses\n• Master blood grouping and cross-matching\n• Understand coagulation mechanisms\n• Identify blood disorders\n• Apply transfusion medicine principles",
                'requirements' => 'Medical Laboratory Technology background, Basic hematology knowledge, Reliable internet connection',
                'category' => (object)['id' => 2, 'name' => 'Hematology', 'slug' => 'hematology'],
                'instructor' => (object)['id' => 2, 'name' => 'Dr. James Kiprotich, MSc', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 45.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => false,
                'price' => 9200.00,
                'discount_price' => 7360.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Hematology Blood Banking KMLTTB Course',
                'meta_description' => 'Professional hematology and blood banking course for KMLTTB certification. Master blood testing and transfusion medicine.',
                'tags' => 'hematology,blood banking,transfusion,KMLTTB,medical laboratory,blood grouping',
                'created_at' => '2024-01-20 09:00:00',
                'updated_at' => '2024-01-20 09:00:00',
            ],
            [
                'id' => 3,
                'title' => 'Medical Microbiology and Infection Control',
                'slug' => 'medical-microbiology-infection-control',
                'short_description' => 'Learn bacterial identification, antimicrobial susceptibility testing, and infection prevention protocols for KMLTTB compliance.',
                'description' => 'Essential microbiology course for laboratory professionals covering bacterial, viral, fungal, and parasitic identification. Includes antimicrobial susceptibility testing, infection control measures, biosafety protocols, and emerging infectious diseases relevant to Kenya. Meets KMLTTB continuing education requirements.',
                'learning_objectives' => "• Identify pathogenic microorganisms\n• Perform antimicrobial susceptibility testing\n• Implement infection control measures\n• Apply biosafety protocols\n• Understand emerging infectious diseases",
                'requirements' => 'Basic microbiology knowledge, Laboratory experience, Internet access for online modules',
                'category' => (object)['id' => 3, 'name' => 'Microbiology', 'slug' => 'microbiology'],
                'instructor' => (object)['id' => 3, 'name' => 'Dr. Grace Muthoni, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 50.00,
                'difficulty_level' => 'advanced',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 10500.00,
                'discount_price' => 8400.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Medical Microbiology KMLTTB Certification',
                'meta_description' => 'Advanced microbiology course for KMLTTB certification. Learn pathogen identification and infection control.',
                'tags' => 'microbiology,infection control,antimicrobial,KMLTTB,pathogen identification,biosafety',
                'created_at' => '2024-01-25 10:00:00',
                'updated_at' => '2024-01-25 10:00:00',
            ],
            [
                'id' => 4,
                'title' => 'Laboratory Quality Management Systems',
                'slug' => 'laboratory-quality-management-systems',
                'short_description' => 'Implement ISO 15189 standards and quality management systems in medical laboratories according to KMLTTB guidelines.',
                'description' => 'Comprehensive course on laboratory quality management covering ISO 15189 implementation, document control, internal audits, corrective actions, and continuous improvement. Essential for laboratory managers and senior technologists seeking KMLTTB compliance and accreditation.',
                'learning_objectives' => "• Understand ISO 15189 requirements\n• Implement quality management systems\n• Conduct internal audits\n• Manage laboratory documentation\n• Ensure regulatory compliance",
                'requirements' => 'Laboratory management experience, Understanding of quality concepts, Computer literacy',
                'category' => (object)['id' => 4, 'name' => 'Quality Management', 'slug' => 'quality-management'],
                'instructor' => (object)['id' => 4, 'name' => 'Dr. Peter Otieno, MSc QM', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 35.00,
                'difficulty_level' => 'advanced',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 7800.00,
                'discount_price' => 6240.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Laboratory Quality Management ISO 15189',
                'meta_description' => 'Master laboratory quality management systems and ISO 15189 standards for KMLTTB compliance.',
                'tags' => 'quality management,ISO 15189,laboratory accreditation,KMLTTB,quality systems',
                'created_at' => '2024-02-01 11:00:00',
                'updated_at' => '2024-02-01 11:00:00',
            ],
            [
                'id' => 5,
                'title' => 'Immunology and Serology Techniques',
                'slug' => 'immunology-serology-techniques',
                'short_description' => 'Master immunological testing methods, ELISA techniques, and serological diagnosis for infectious diseases.',
                'description' => 'Advanced course in immunology and serology covering immune system principles, antigen-antibody reactions, ELISA procedures, rapid diagnostic tests, and interpretation of immunological results. Includes HIV testing, hepatitis serology, and autoimmune disease markers.',
                'learning_objectives' => "• Understand immune system mechanisms\n• Perform ELISA and immunoassays\n• Interpret serological results\n• Conduct HIV and hepatitis testing\n• Apply immunological principles",
                'requirements' => 'Basic immunology knowledge, Laboratory experience, Understanding of infectious diseases',
                'category' => (object)['id' => 5, 'name' => 'Immunology', 'slug' => 'immunology'],
                'instructor' => (object)['id' => 5, 'name' => 'Dr. Sarah Njeri, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 42.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 8900.00,
                'discount_price' => 7120.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Immunology Serology KMLTTB Course',
                'meta_description' => 'Professional immunology and serology course for medical laboratory technologists. Master immunoassays and diagnostic testing.',
                'tags' => 'immunology,serology,ELISA,HIV testing,hepatitis,autoimmune,KMLTTB',
                'created_at' => '2024-02-05 12:00:00',
                'updated_at' => '2024-02-05 12:00:00',
            ],
            [
                'id' => 6,
                'title' => 'Laboratory Safety and Biosecurity Fundamentals',
                'slug' => 'laboratory-safety-biosecurity-fundamentals',
                'short_description' => 'Essential safety protocols, waste management, and biosecurity measures for medical laboratories in Kenya.',
                'description' => 'Critical course covering laboratory safety protocols, chemical safety, biological hazard management, waste disposal procedures, emergency response, and biosecurity measures. Designed to meet KMLTTB safety requirements and protect laboratory personnel.',
                'learning_objectives' => "• Implement laboratory safety protocols\n• Manage biological and chemical hazards\n• Apply proper waste disposal methods\n• Respond to laboratory emergencies\n• Ensure biosecurity compliance",
                'requirements' => 'Basic laboratory knowledge, No prior safety training required, Commitment to safety practices',
                'category' => (object)['id' => 6, 'name' => 'Laboratory Safety', 'slug' => 'laboratory-safety'],
                'instructor' => (object)['id' => 6, 'name' => 'Dr. Michael Ochieng, MSc Safety', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 25.00,
                'difficulty_level' => 'beginner',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => true,
                'price' => 0.00,
                'discount_price' => 0.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Laboratory Safety Biosecurity Training',
                'meta_description' => 'Free laboratory safety and biosecurity course for medical laboratory professionals. Learn essential safety protocols.',
                'tags' => 'laboratory safety,biosecurity,waste management,emergency response,KMLTTB,free course',
                'created_at' => '2024-02-10 13:00:00',
                'updated_at' => '2024-02-10 13:00:00',
            ],
        ];
    }

    /**
     * Get static category data for Kenyan Laboratory CPD Provider
     */
    private function getStaticCategoryData()
    {
        return [
            (object)[
                'id' => 1,
                'name' => 'Clinical Chemistry',
                'slug' => 'clinical-chemistry',
                'description' => 'Biochemical analysis, enzyme studies, and clinical chemistry techniques',
                'color' => '#3498db',
                'icon' => 'fas fa-flask',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 2,
                'name' => 'Hematology',
                'slug' => 'hematology',
                'description' => 'Blood analysis, coagulation studies, and transfusion medicine',
                'color' => '#e74c3c',
                'icon' => 'fas fa-tint',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 3,
                'name' => 'Microbiology',
                'slug' => 'microbiology',
                'description' => 'Pathogen identification, antimicrobial testing, and infection control',
                'color' => '#2ecc71',
                'icon' => 'fas fa-microscope',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 4,
                'name' => 'Quality Management',
                'slug' => 'quality-management',
                'description' => 'ISO standards, quality systems, and laboratory accreditation',
                'color' => '#f39c12',
                'icon' => 'fas fa-award',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 5,
                'name' => 'Immunology',
                'slug' => 'immunology',
                'description' => 'Immunoassays, serology, and diagnostic immunology',
                'color' => '#9b59b6',
                'icon' => 'fas fa-shield-alt',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 6,
                'name' => 'Laboratory Safety',
                'slug' => 'laboratory-safety',
                'description' => 'Safety protocols, biosecurity, and emergency procedures',
                'color' => '#e67e22',
                'icon' => 'fas fa-hard-hat',
                'is_active' => true,
                'courses_count' => 1,
            ],
        ];
    }
}
