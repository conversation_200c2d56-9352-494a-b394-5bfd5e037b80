@extends('layouts.payment')

@section('title', 'Payment Status')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="payment-status-card">
                        <div class="text-center mb-4">
                            <div class="payment-status-icon" id="status-icon">
                                @if($transaction->transaction_status === 'pending')
                                    <div class="spinner-border text-warning" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                @elseif($transaction->transaction_status === 'paid')
                                    <i class="fas fa-check-circle text-success fa-4x"></i>
                                @elseif($transaction->transaction_status === 'failed')
                                    <i class="fas fa-times-circle text-danger fa-4x"></i>
                                @endif
                            </div>
                            
                            <h2 class="payment-status-title mt-3" id="status-title">
                                @if($transaction->transaction_status === 'pending')
                                    Processing Payment...
                                @elseif($transaction->transaction_status === 'paid')
                                    Payment Successful!
                                @elseif($transaction->transaction_status === 'failed')
                                    Payment Failed
                                @endif
                            </h2>
                            
                            <p class="payment-status-message" id="status-message">
                                @if($transaction->transaction_status === 'pending')
                                    Please wait while we confirm your M-Pesa payment. This usually takes a few seconds.
                                @elseif($transaction->transaction_status === 'paid')
                                    Your payment has been confirmed and you have been enrolled in your courses.
                                @elseif($transaction->transaction_status === 'failed')
                                    Your payment could not be processed. Please try again.
                                @endif
                            </p>
                        </div>

                        <div class="payment-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Transaction ID:</span>
                                        <span class="value">{{ $transaction->transaction_request_id }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Amount:</span>
                                        <span class="value">KES {{ number_format($transaction->amount, 2) }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Phone Number:</span>
                                        <span class="value">{{ $transaction->phone_number }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-item">
                                        <span class="label">Status:</span>
                                        <span class="value status-badge" id="status-badge">
                                            @if($transaction->transaction_status === 'pending')
                                                <span class="badge bg-warning">Processing</span>
                                            @elseif($transaction->transaction_status === 'paid')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($transaction->transaction_status === 'failed')
                                                <span class="badge bg-danger">Failed</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($transaction->transaction_status === 'pending')
                            <div class="payment-progress mt-4">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" 
                                         role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <p class="text-center mt-2 text-muted">
                                    <small>Checking payment status... <span id="countdown">60</span>s</small>
                                </p>
                            </div>
                        @endif

                        <div class="payment-actions mt-4 text-center">
                            @if($transaction->transaction_status === 'paid')
                                <a href="{{ route('user.dashboard') }}" class="btn btn-success btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                                </a>
                            @elseif($transaction->transaction_status === 'failed')
                                <a href="{{ route('cart.index') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-redo me-2"></i>Try Again
                                </a>
                            @else
                                <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                                    <i class="fas fa-sync me-2"></i>Refresh Status
                                </button>
                            @endif
                        </div>
        </div>
    </div>
</div>

<style>
.payment-status-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.payment-status-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.payment-status-icon {
    margin-bottom: 20px;
}

.payment-status-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.payment-status-message {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
}

.payment-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin: 30px 0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.detail-item .label {
    font-weight: 600;
    color: #495057;
}

.detail-item .value {
    color: #333;
    font-weight: 500;
}

.payment-progress {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.progress {
    height: 8px;
    border-radius: 10px;
}

.payment-actions .btn {
    min-width: 200px;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
}

@media (max-width: 768px) {
    .payment-status-card {
        padding: 25px;
        margin: 15px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

@if($transaction->transaction_status === 'pending')
<script>
let countdown = 60;
let checkInterval;

function updateCountdown() {
    document.getElementById('countdown').textContent = countdown;
    countdown--;
    
    if (countdown < 0) {
        clearInterval(checkInterval);
        window.location.reload();
    }
}

function checkPaymentStatus() {
    fetch(`{{ route('payment.check', $transaction->transaction_request_id) }}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                if (data.transaction_status === 'paid') {
                    // Payment successful - redirect to dashboard
                    window.location.href = data.redirect_url;
                } else if (data.transaction_status === 'failed') {
                    // Payment failed - redirect to cart
                    window.location.href = data.redirect_url;
                }
                // If still pending, continue checking
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
        });
}

// Start checking payment status every 5 seconds
setInterval(checkPaymentStatus, 5000);

// Start countdown
checkInterval = setInterval(updateCountdown, 1000);

// Initial check
checkPaymentStatus();
</script>
@endif
@endsection
