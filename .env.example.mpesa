# M-Pesa Configuration
# Get these credentials from Safaricom Developer Portal: https://developer.safaricom.co.ke/

# Environment: sandbox or production
MPESA_ENVIRONMENT=sandbox

# Consumer Key and Secret from your app
MPESA_CONSUMER_KEY=your_consumer_key_here
MPESA_CONSUMER_SECRET=your_consumer_secret_here

# Passkey for STK Push (get from Safaricom)
MPESA_PASSKEY=your_passkey_here

# Business Shortcode (Paybill or Till Number)
MPESA_SHORTCODE=174379

# Callback URLs (update with your domain)
MPESA_CALLBACK_URL="${APP_URL}/checkout/mpesa/callback"
MPESA_RESULT_URL="${APP_URL}/checkout/mpesa/result"
MPESA_TIMEOUT_URL="${APP_URL}/checkout/mpesa/timeout"

# Logging settings
MPESA_LOGGING_ENABLED=true
MPESA_LOGGING_LEVEL=info
MPESA_LOGGING_CHANNEL=daily

# Test credentials for sandbox (DO NOT use in production)
# Consumer Key: your_test_consumer_key
# Consumer Secret: your_test_consumer_secret
# Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
# Shortcode: 174379
# Test Phone: 254708374149
