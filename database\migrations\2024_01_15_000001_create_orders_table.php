<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Billing information
            $table->string('billing_name');
            $table->string('billing_email');
            $table->string('billing_phone');

            // Payment information
            $table->enum('payment_method', ['mpesa', 'card', 'bank_transfer']);
            $table->decimal('subtotal', 10, 2);
            $table->decimal('total', 10, 2);

            // Order status
            $table->enum('status', ['pending', 'processing', 'completed', 'cancelled'])->default('pending');
            $table->enum('payment_status', ['pending', 'completed', 'failed', 'pending_verification'])->default('pending');

            // M-Pesa specific fields
            $table->string('mpesa_checkout_request_id')->nullable();
            $table->string('mpesa_receipt_number')->nullable();
            $table->string('payment_reference')->nullable();

            // Timestamps
            $table->timestamp('paid_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['payment_status']);
            $table->index(['payment_method']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
