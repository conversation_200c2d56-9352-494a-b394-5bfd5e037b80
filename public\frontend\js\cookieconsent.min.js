/*!
 * <PERSON>ie Consent by Osano
 * Basic implementation for cookie consent
 */
(function() {
    'use strict';

    window.wpcc = {
        init: function(options) {
            // Basic cookie consent implementation
            var cookieName = 'cookie_consent';
            var cookieValue = this.getCookie(cookieName);

            if (!cookieValue) {
                this.showBanner(options);
            }
        },

        getCookie: function(name) {
            var value = "; " + document.cookie;
            var parts = value.split("; " + name + "=");
            if (parts.length == 2) return parts.pop().split(";").shift();
        },

        setCookie: function(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        },

        showBanner: function(options) {
            var banner = document.createElement('div');
            banner.className = 'wpcc-container wpcc-bottom wpcc-type-opt-out wpcc-theme-block wpcc-color--default';
            banner.innerHTML = `
                <div class="wpcc-corner-top-left"></div>
                <div class="wpcc-corner-top-right"></div>
                <div class="wpcc-corner-bottom-left"></div>
                <div class="wpcc-corner-bottom-right"></div>
                <div class="wpcc-background"></div>
                <div class="wpcc-content">
                    <span class="wpcc-message">${options.content.message}</span>
                    <a href="${options.content.href}" class="wpcc-link">${options.content.link}</a>
                    <button class="wpcc-btn">${options.content.button}</button>
                </div>
            `;

            // Apply styles
            banner.style.cssText = `
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: ${options.colors.popup.background};
                color: ${options.colors.popup.text};
                padding: 15px;
                z-index: 9999;
                font-family: Arial, sans-serif;
                font-size: 14px;
                line-height: 1.5;
            `;

            var button = banner.querySelector('.wpcc-btn');
            button.style.cssText = `
                background: ${options.colors.button.background};
                color: ${options.colors.button.text};
                border: none;
                padding: 8px 16px;
                margin-left: 10px;
                cursor: pointer;
                border-radius: 4px;
            `;

            var link = banner.querySelector('.wpcc-link');
            link.style.cssText = `
                color: ${options.colors.popup.text};
                text-decoration: underline;
                margin: 0 10px;
            `;

            document.body.appendChild(banner);

            // Handle button click
            var self = this;
            button.addEventListener('click', function() {
                self.setCookie('cookie_consent', 'accepted', 365);
                banner.style.display = 'none';
            });
        }
    };
})();
