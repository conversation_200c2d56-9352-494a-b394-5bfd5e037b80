@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Completed Courses</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Completed Courses</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle mb-0">
                                    <thead>
                                        <tr>
                                            <th>Course</th>
                                            <th>Instructor</th>
                                            <th>Completed Date</th>
                                            <th>Certificate</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($enrollments as $enrollment)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        @if($enrollment->course->thumbnail)
                                                            <img src="{{ asset('storage/' . $enrollment->course->thumbnail) }}" alt="" class="img-fluid rounded">
                                                        @else
                                                            <div class="avatar-title rounded bg-success">
                                                                {{ substr($enrollment->course->title, 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <h5 class="font-size-14 mb-1">{{ $enrollment->course->title }}</h5>
                                                        <p class="text-muted mb-0">{{ $enrollment->course->category->name ?? 'General' }}</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $enrollment->course->instructor->name ?? 'N/A' }}</td>
                                            <td>{{ $enrollment->completed_at->format('M d, Y') }}</td>
                                            <td>
                                                @if($enrollment->course->certificate)
                                                    <span class="badge badge-soft-success">Available</span>
                                                @else
                                                    <span class="badge badge-soft-secondary">Not Available</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($enrollment->course->certificate)
                                                    <a href="{{ route('user.certificates') }}" class="btn btn-success btn-sm">View Certificate</a>
                                                @endif
                                                <a href="{{ route('course.show', $enrollment->course->slug) }}" class="btn btn-outline-primary btn-sm">Review</a>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <p class="text-muted mb-0">No completed courses found.</p>
                                                <a href="{{ route('visitors.courses') }}" class="btn btn-primary btn-sm mt-2">Browse Courses</a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            
                            @if($enrollments->hasPages())
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="pagination-wrap hstack gap-2 justify-content-center">
                                        {{ $enrollments->links() }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection