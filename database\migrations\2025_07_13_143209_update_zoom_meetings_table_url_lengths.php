<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('zoom_meetings', function (Blueprint $table) {
            // Increase URL field lengths to accommodate long Zoom URLs with JWT tokens
            $table->text('join_url')->change();
            $table->text('start_url')->change();
            $table->text('recording_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('zoom_meetings', function (Blueprint $table) {
            // Revert back to string(255) - note: this may cause data loss if URLs are longer
            $table->string('join_url')->change();
            $table->string('start_url')->change();
            $table->string('recording_url')->nullable()->change();
        });
    }
};
