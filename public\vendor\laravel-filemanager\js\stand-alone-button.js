(function($) {
    'use strict';

    $.fn.filemanager = function(type, options) {
        type = type || 'file';

        this.on('click', function(e) {
            e.preventDefault();

            var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
            var target_input = $('#' + $(this).data('input'));
            var target_preview = $('#' + $(this).data('preview'));

            // Simple fallback - just show an alert for now
            alert('File manager functionality requires Laravel File Manager package to be installed and configured.');

            return false;
        });

        return this;
    };
})(jQuery);
